# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
```bash
# Development server with hot reload
npm run dev

# Production build (includes 404.html copy for SPA routing)
npm run build

# Code quality checks - ALWAYS run after making changes
npm run lint          # ESLint with max 0 warnings
npm run typecheck     # TypeScript type checking
npm run test          # Run Vitest test suite
npm run test:watch    # Watch mode for testing

# Preview production build
npm run preview
```

### Quality Assurance Commands
Always run these commands before committing changes:
```bash
# Search for potential issues
grep -r "console\." src/ --include="*.ts" --include="*.tsx"  # Find console.logs
grep -r ": any" src/ --include="*.ts" --include="*.tsx"     # Find any types
```

## Architecture Overview

### API Layer Architecture
- **Centralized Axios configuration** with automatic token management and retry logic
- **WebSocket streaming** for real-time chat via `WebSocketManager.ts`
- **Mock server support** using MirageJS (toggle with `VITE_USE_MOCK_SERVER=true`)
- **Authentication flow** with JWT tokens, automatic refresh, and session management
- **Modular API structure**: `authApi.ts`, `chatApi.ts`, `agentsApi.ts`, etc.
- **Error handling** with standardized `ApiError` interface and retry mechanisms

### Component Architecture (Atomic Design)
```
src/components/
├── atoms/        # Basic UI: Button, Input, Icon, Avatar (no business logic)
├── molecules/    # Combinations: MessageBubble, SearchBar (local state allowed)
├── organisms/    # Complex: StudioChatInterface, ConversationList (Redux connected)
└── templates/    # Page layouts: StudioTemplate, LoginTemplate
```

### State Management (Redux Toolkit)
```typescript
// Store structure
{
  auth: AuthState,                    // User authentication, persisted
  chatConversations: ChatConversationsState,  // Chat data, not persisted
  workspaces: WorkspacesState,        // Project/workspace data
  ui: UIState,                        // Modal states, theme, persisted
  agents: AgentsState,                // AI agents configuration
  workflows: WorkflowsState           // Workflow management
}
```

### Key Integration Patterns
- **Custom hooks pattern**: Use `useChatConversations()` for chat operations (70+ actions/selectors)
- **WebSocket streaming**: Real-time chat responses with cancellation support
- **File uploads**: FormData with progress tracking and validation
- **LaTeX rendering**: Mathematical formulas using `react-katex`
- **Authentication**: Azure MSAL integration for SSO

## Critical Code Standards

### Logging (MANDATORY)
```typescript
// ✅ REQUIRED - Use logger utility
import { logger } from '@/shared/utils/logger';
logger.dev("Debug info", data);
logger.error("Error occurred", error);

// ❌ PROHIBITED - Direct console usage
console.log("Debug info");
```

### Import Patterns (MANDATORY)
```typescript
// ✅ REQUIRED - Use barrel exports
import { Button, Input } from '@/components/atoms';
import { SearchBar } from '@/components/molecules';

// ❌ AVOID - Direct imports
import Button from '@/components/atoms/Button';
```

### TypeScript Standards
- **Strict mode enabled** - no `any` types allowed
- **Comprehensive interfaces** for all props and API responses
- **Generic patterns** for reusable types

## Environment Configuration

### Development Environment
```bash
# Mock server for development
VITE_USE_MOCK_SERVER=true

# API configuration
VITE_API_BASE_URL=https://cognit-ai-s3q92.ondigitalocean.app

# Feature flags
VITE_ALLOW_PREVIEW_FEATURES=true
```

### LLM Provider Configuration
Supports multiple AI providers via `src/shared/config.ts`:
- OpenAI (GPT-4 Turbo, GPT-3.5)
- Anthropic (Claude-3 Opus, Sonnet)
- Google (Gemini Pro)
- Meta (Llama-2-70B)

## Testing Strategy

### Test Setup
- **Vitest** for unit testing with jsdom environment
- **Testing Library** for component testing
- **Test coverage** tracking with v8
- **Mock API responses** using MirageJS

### Running Tests
```bash
npm run test                    # Run all tests once
npm run test:watch             # Watch mode
npm run test -- MessageBubble  # Run specific test file
```

## Key Components to Understand

### StudioChatInterface (`src/components/organisms/StudioChatInterface.tsx`)
- 1,158 lines - main chat interface
- WebSocket streaming integration
- File upload with drag-and-drop
- Model selection and agent configuration
- Auto-scroll management during streaming

### MessageBubble (`src/components/molecules/MessageBubble.tsx`)
- Rich content rendering (LaTeX, Markdown, code)
- Interactive features (copy, like/dislike, edit)
- File attachment display
- Responsive design for different message types

### WebSocketManager (`src/api/WebSocketManager.ts`)
- Connection pooling by session ID
- Message streaming with chunked responses
- Auto-reconnection and error handling
- File integration support

## Development Guidelines

### Adding New Features
1. Follow Atomic Design hierarchy
2. Use existing custom hooks for state management
3. Implement proper TypeScript interfaces
4. Add corresponding mock endpoints if needed
5. Write tests for new components
6. Run quality assurance commands

### Working with Chat Features
- Use `useChatConversations()` hook for chat operations
- Stream responses via WebSocket for real-time experience
- Handle file uploads through `useFileUpload()` hook
- Implement LaTeX rendering for mathematical content

### Authentication Integration
- All API calls automatically include authentication headers
- Use `useAuth()` hook for authentication state
- Handle token refresh automatically via axios interceptors
- Support both manual login and SSO via Azure MSAL

### Performance Considerations
- Components use `React.memo` for optimization
- Redux state is selectively persisted
- WebSocket connections are pooled and reused
- Code splitting at template level

## Build and Deployment

### Production Build
```bash
npm run build
```
- Creates optimized bundle in `dist/`
- Copies `index.html` to `404.html` for SPA routing
- Removes console.logs (except in mock mode)
- Enables Terser minification

### Mock vs Production Mode
- Development: Mock server provides realistic API responses
- Production: Real API at `https://cognit-ai-s3q92.ondigitalocean.app`
- Toggle via `VITE_USE_MOCK_SERVER` environment variable