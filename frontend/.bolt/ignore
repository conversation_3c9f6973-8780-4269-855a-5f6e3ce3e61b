# ===== FOCO NA PÁGINA DE WORKFLOWS (AGENTS) =====
# Configuração para trabalhar apenas com a funcionalidade de workflows

# Dependencies e build artifacts (SEMPRE ignorar)
node_modules/
dist/
build/
.next/
.nuxt/
.vercel/
.netlify/

# Package managers (SEMPRE ignorar - muito pesados)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Configurações (ignorar a menos que esteja editando configs)
vite.config.ts
vitest.config.ts
tailwind.config.js
postcss.config.js
eslint.config.js
tsconfig*.json

# Documentação (ignorar para reduzir tokens)
README.md
docs/
CLAUDE.md
PLANNING.md
PRD.md
TASKS.md

# Testes (ignorar para economizar tokens)
**/__tests__/
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
src/test/
setupTests.ts

# Version control
.git/
.gitignore

# IDE e OS
.vscode/
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== IGNORAR PÁGINAS NÃO RELACIONADAS AOS WORKFLOWS =====
src/pages/AuthPage.tsx
src/pages/StudioPage.tsx

# ===== IGNORAR TEMPLATES NÃO RELACIONADOS AOS WORKFLOWS =====
src/components/templates/LoginTemplate.tsx
src/components/templates/StudioTemplate.tsx

# ===== IGNORAR ORGANISMS NÃO RELACIONADOS AOS WORKFLOWS =====
src/components/organisms/ChatInterface.tsx
src/components/organisms/ConversationList.tsx
src/components/organisms/ConversationSidebar.tsx
src/components/organisms/Header.tsx
src/components/organisms/LoginCard.tsx
src/components/organisms/MemberManagement.tsx
src/components/organisms/SearchInterface.tsx
src/components/organisms/StudioChatInterface.tsx
src/components/organisms/StudioHeader.tsx
src/components/organisms/StudioHistoryModal.tsx
src/components/organisms/StudioKnowledgeModal.tsx

# ===== IGNORAR MOLECULES NÃO RELACIONADAS AOS WORKFLOWS =====
src/components/molecules/GoogleLoginButton.tsx
src/components/molecules/LoginHeader.tsx
src/components/molecules/PasswordInput.tsx
src/components/molecules/UserProfile.tsx
src/components/molecules/WorkspaceFilter.tsx

# ===== IGNORAR HOOKS NÃO RELACIONADOS AOS WORKFLOWS =====
src/hooks/useAuth.ts
src/hooks/useChat.ts
src/hooks/useConversations.ts
src/hooks/useGoogleAuth.ts
src/hooks/useMessageStreaming.ts
src/hooks/useStreamProcessor.ts
src/hooks/useStreaming.ts
src/hooks/useWorkspaces.ts

# ===== IGNORAR APIs NÃO RELACIONADAS AOS WORKFLOWS =====
src/api/authApi.ts
src/api/chatApi.ts
src/api/documentApi.ts
src/api/memberApi.ts
src/api/projectApi.ts
src/api/workspaceApi.ts
src/api/__tests__/

# ===== IGNORAR REDUX MODULES NÃO RELACIONADOS AOS WORKFLOWS =====
src/redux/auth/
src/redux/chat/
src/redux/conversations/
src/redux/ui/
src/redux/workspaces/

# ===== IGNORAR UTILS NÃO RELACIONADOS AOS WORKFLOWS =====
src/shared/utils/workspaceUtils.ts

# ===== IGNORAR OUTROS ARQUIVOS NÃO ESSENCIAIS =====
public/
index.html
src/vite-env.d.ts
src/styles/
quick-check.sh