import React from 'react';
import { ProviderIcon } from './ProviderIcon';
import { getProviderFromModel, getProviderGradient } from '../../shared/utils/providerUtils';

interface TypingIndicatorProps {
  modelName: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  modelName
}) => {
  const provider = getProviderFromModel(modelName);
  const gradientColor = getProviderGradient(provider);

  return (
    <div className="flex items-start space-x-4 animate-fade-in">
      <div className={`w-10 h-10 bg-gradient-to-br ${gradientColor} rounded-full flex items-center justify-center shadow-lg`}>
        <ProviderIcon 
          provider={provider} 
          size="sm" 
          variant="monochrome" 
          className="w-5 h-5 text-white animate-pulse" 
        />
      </div>
      <div className="flex-1">
        <div className="mb-2">
          <span className="text-sm font-semibold text-gray-700">{modelName}</span>
        </div>
        {/* Typing Indicator - No Bubble, Direct Text */}
        <div className="mr-8 text-base leading-relaxed text-gray-800">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce delay-100"></div>
              <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce delay-200"></div>
            </div>
            <span className="text-sm text-gray-600 font-medium">
              está pensando...
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};