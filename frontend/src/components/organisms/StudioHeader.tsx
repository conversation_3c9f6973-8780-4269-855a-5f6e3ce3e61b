import React from 'react';
import { Database } from 'lucide-react';
import { config } from '../../shared/config';
import '../../styles/components/organisms/studio-header.css';

interface StudioHeaderProps {
  onKnowledgeBaseClick?: () => void;
  className?: string;
}

export const StudioHeader: React.FC<StudioHeaderProps> = ({
  onKnowledgeBaseClick,
  className = '',
}) => {
  return (
    <header className={`studio-header border-b border-gray-200 px-3 py-4 flex items-center justify-between z-50 ${className}`}>

      {/* Ações do cabeçalho */}
      <div className="flex items-center gap-3">
        {/* Botão de Base de Conhecimento - Preview Feature */}
        {config.ALLOW_PREVIEW_FEATURES && onKnowledgeBaseClick && (
          <button
            onClick={onKnowledgeBaseClick}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="Base de Conhecimento"
          >
            <Database className="w-4 h-4" />
            <span className="text-sm font-medium">Base de Conhecimento</span>
          </button>
        )}
      </div>
    </header>
  );
};
