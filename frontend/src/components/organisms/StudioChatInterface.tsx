import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Paperclip, X, ChevronDown, Sparkles, Square, Wrench, ArrowDown, Calculator, Globe, FileText, Image, Code, BarChart3, Database } from 'lucide-react';
import { MessageBubble, TypingIndicator, StreamingMessage } from '../molecules';
import { ToolsDropdown } from '../molecules/ToolsDropdown';
import { ProviderIcon } from '../molecules/ProviderIcon';
import { useChatConversations } from '../../hooks/useChatConversations';
import { useStreaming } from '../../hooks/useStreaming';
import { useAppDispatch } from '../../redux/store';
import { fetchConversations } from '../../redux/chatConversations/chatConversationsActions';
import { logger } from '../../shared/utils';
import { chatApi } from '../../api/chatApi';
import { config } from '../../shared/config';
import { formatFileSize, getFileIcon, getFileTypeLabel } from '../../shared/utils/fileUtils';
import { mockTools } from '../../api/mock/mockData';
import type { LLMModel, Message, Tool } from '../../types';

interface StudioChatInterfaceProps {
  className?: string;
}

export const StudioChatInterface: React.FC<StudioChatInterfaceProps> = ({
  className = ''
}) => {
  // Redux hooks
  const dispatch = useAppDispatch();
  const {
    currentConversation,
    messages,
    isLoading,
    isTyping,
    selectedModel: reduxSelectedModel,
    sendQuickMessage,
    regenerateLastMessage,
    changeModel,
    addNewMessage,
    updateChatMessage,
    removeMessageAction: removeMessagesAfterMessage,
    removeMessagesAfterAction,
    setTyping,
    streamingMessage,
    selectConversation,
    lastMessage,
  } = useChatConversations();

  const { isStreaming, startStreaming, stopStreaming } = useStreaming();

  // Local UI state
  const [availableModels, setAvailableModels] = useState<LLMModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<LLMModel | null>(null);
  const [message, setMessage] = useState('');
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [showToolsMenu, setShowToolsMenu] = useState(false);
  const [focusedModelIndex, setFocusedModelIndex] = useState(-1);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);

  // File upload state
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [_isUploadingFiles, _setIsUploadingFiles] = useState(false);
  const [_uploadProgress, _setUploadProgress] = useState(0);
  const [uploadedFileIds, setUploadedFileIds] = useState<string[]>([]);

  // Drag & drop state
  const [isDragOver, setIsDragOver] = useState(false);
  const [_dragCounter, _setDragCounter] = useState(0);

  // Scroll state
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // Cancel control state - prevents canceling for 2 seconds after sending
  const [canCancel, setCanCancel] = useState(true);

  // Load models from API
  useEffect(() => {
    const loadModels = async () => {
      try {
        const models = await chatApi.getModels();
        setAvailableModels(models);
        if (models.length > 0 && !selectedModel) {
          setSelectedModel(models[0]);
        }
      } catch (error) {
        logger.error('Erro ao carregar modelos', error);
      }
    };

    loadModels();
  }, [selectedModel]);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Simple scroll to bottom function
  const scrollToBottom = () => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth'
    });
  };

  // Check if user is near bottom of scroll
  const checkIfNearBottom = () => {
    if (!messagesContainerRef.current) return true;

    const container = messagesContainerRef.current;
    const threshold = 100; // pixels from bottom
    const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < threshold;

    return isNearBottom;
  };

  // Handle manual scroll by user
  const handleScroll = () => {
    const isNearBottom = checkIfNearBottom();

    // If user scrolled away from bottom, disable auto scroll
    if (!isNearBottom) {
      setShouldAutoScroll(false);
      setShowScrollButton(true);
    } else {
      // If user scrolled back to bottom, enable auto scroll
      setShouldAutoScroll(true);
      setShowScrollButton(false);
    }
  };

  // Handle scroll button click
  const handleScrollButtonClick = () => {
    scrollToBottom();
    setShouldAutoScroll(true);
    setShowScrollButton(false);
  };

  // Auto scroll when user sends a message
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];

      // If user sent a message, enable auto scroll and scroll to bottom
      // This will position for the upcoming assistant response
      if (lastMessage.role === 'user') {
        setShouldAutoScroll(true);
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      }
    }
  }, [messages]);

  // Auto scroll during streaming (if auto scroll is enabled)
  useEffect(() => {
    if (shouldAutoScroll && streamingMessage) {
      // Execute scroll immediately instead of using timeout that gets cancelled
      scrollToBottom();
    }
  }, [streamingMessage, shouldAutoScroll]);



  // Sync model selection with Redux
  useEffect(() => {
    const model = availableModels.find((m) => m.id === reduxSelectedModel);
    if (model && model.id !== selectedModel?.id) {
      setSelectedModel(model);
    }
  }, [reduxSelectedModel, selectedModel?.id, availableModels]);

  const handleModelSelect = useCallback((model: LLMModel) => {
    setSelectedModel(model);
    changeModel(model.id);
  }, [changeModel]);


  const handleSendOrStop = () => {
    if (isStreaming || streamingMessage || isTyping) {
      // Only allow canceling if canCancel is true
      if (canCancel) {
        stopStreaming();
        setTyping(false);
      }
    } else {
      sendMessage();
    }
  };

  const sendMessage = async () => {
    if (!message.trim() || isLoading || isStreaming || isUploadingFiles) return;

    const messageContent = message.trim();
    const files = [...attachedFiles];

    // Upload file first if there is one (only 1 file supported)
    let fileId: string | undefined;
    if (files.length > 0) {
      try {
        const file = files[0]; // Only take the first file
        logger.dev('🔄 Iniciando upload de arquivo antes do WebSocket', {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        });

        const uploadResult = await chatApi.uploadFileForChat(file);
        fileId = uploadResult.file_id;
        setUploadedFileIds([fileId]); // Keep array for compatibility

        logger.dev('✅ Upload concluído, file_id recebido', {
          fileId,
          filename: uploadResult.filename
        });
      } catch (error) {
        logger.error('❌ Erro ao fazer upload do arquivo', error);
        return; // Don't send message if file upload fails
      }
    }

    // Verificar se é uma nova conversa (sem sessionId do banco)
    const isNewConversation = !currentConversation?.sessionId || messages.length === 0;

    // Debug específico para fluxo de conversas
    console.log('🚀 StudioChatInterface Send Debug:', {
      isNewConversation,
      currentSessionId: currentConversation?.sessionId,
      messagesCount: messages.length,
      currentConversation: currentConversation ? {
        id: currentConversation.id,
        sessionId: currentConversation.sessionId,
        title: currentConversation.title
      } : null
    });

    // Clear input immediately
    setMessage('');
    setAttachedFiles([]);
    setUploadedFileIds([]);

    // Add user message immediately to show it in the UI
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: messageContent,
      role: 'user',
      timestamp: new Date().toISOString(),
      conversationId: currentConversation?.sessionId || 'temp',
      attachments: files.length > 0 ? files.map((file, index) => ({
        id: `attachment-${Date.now()}-${index}`,
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file), // URL temporária para visualização
      })) : undefined,
    };

    addNewMessage(userMessage);
    
    console.log('✅ Mensagem do usuário adicionada:', {
      messageId: userMessage.id,
      content: userMessage.content.substring(0, 50),
      conversationId: userMessage.conversationId,
      messagesCountAfter: messages.length + 1 // +1 porque o Redux ainda não atualizou
    });

    // Validate that a model is selected
    if (!selectedModel) {
      logger.error('Nenhum modelo selecionado');
      return;
    }

    try {
      // Check if provider exists before calling toLowerCase
      if (!selectedModel.provider) {
        logger.error('Modelo selecionado não possui provider');
        return;
      }

      // Set typing state before starting streaming
      setTyping(true);

      // Disable canceling for 1 second after sending
      setCanCancel(false);
      setTimeout(() => {
        setCanCancel(true);
      }, 1000);

      // Start streaming the response (this will handle both sending and receiving)
      logger.dev('🚀 Iniciando streaming com file_id', {
        messageLength: messageContent.length,
        fileId,
        hasFileId: !!fileId,
        modelId: selectedModel.id,
        sessionId: currentConversation?.sessionId
      });

      await startStreaming(
        messageContent,
        {
          modelId: selectedModel.id,
          modelName: selectedModel.name,
          provider: selectedModel.provider.toLowerCase(),
          sessionId: currentConversation?.sessionId, // Use sessionId as primary identifier
          fileId: fileId,
          onStart: () => {
            logger.mock('Streaming started');
          },
          onSessionCreated: (sessionId: string) => {
            console.log('🆕 Nova sessão criada:', sessionId);
            console.log('📊 Estado atual no onSessionCreated:', {
              isNewConversation,
              currentConversation: currentConversation?.sessionId,
              messagesCount: messages.length,
              messages: messages.map(m => ({ role: m.role, content: m.content.substring(0, 50) }))
            });

            // Para novas conversas, atualizar o sessionId e adicionar ao histórico imediatamente
            if (isNewConversation) {
              // Usar setTimeout para garantir que o Redux processou o addMessage primeiro
              setTimeout(() => {
                const updatedConversation = {
                  id: sessionId,
                  sessionId: sessionId,
                  title: 'Nova conversa',
                  messages: [], // Sera preservado pelo reducer se ja existirem mensagens
                  provider: selectedModel.provider,
                  model: selectedModel.id,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  isFavorite: false,
                  tags: [],
                  summary: '',
                  totalTokens: 0,
                  totalCost: 0,
                  lastMessageAt: new Date().toISOString(),
                  messageCount: 0 // Sera atualizado pelo reducer
                };

                console.log('🔄 Atualizando conversa com sessionId (após delay):', {
                  sessionId,
                  currentMessagesCount: messages.length
                });

                // O reducer ira preservar automaticamente as mensagens existentes
                selectConversation(updatedConversation);

                // Não atualizar o histórico aqui - será feito no onComplete quando a resposta estiver pronta
                console.log('📋 Sessão criada, aguardando resposta para atualizar histórico');
              }, 10); // Delay mínimo para garantir que Redux processou addMessage
            }
          },
          onComplete: () => {
            logger.mock('Streaming completed');
            setTyping(false);

            console.log('🏁 onComplete - Estado antes de fetchConversations:', {
              isNewConversation,
              currentConversation: currentConversation?.sessionId,
              messagesCount: messages.length
            });

            // Se é uma nova conversa, atualiza o histórico com delay para evitar conflitos
            if (isNewConversation) {
              logger.dev('Nova conversa criada, atualizando histórico...');

              // Usar setTimeout para evitar conflitos de estado durante o streaming
              setTimeout(() => {
                dispatch(fetchConversations({}));
                console.log('📋 fetchConversations disparado com delay');
              }, 1000); // 1 segundo de delay
            }
          },
          onError: async (error) => {
            logger.error('Streaming error:', error);
            setTyping(false);
            // Não usar fallback quando há arquivo anexado
            // O WebSocket deve ser a única forma de envio com file_id
            if (!fileId) {
              // Só usar fallback se não há arquivo
              try {
                await sendQuickMessage(messageContent);

                // Se é uma nova conversa e o fallback teve sucesso, atualiza o histórico
                if (isNewConversation) {
                  logger.dev('Nova conversa criada via fallback, atualizando histórico...');
                  setTimeout(() => {
                    dispatch(fetchConversations({}));
                  }, 1000);
                }
              } catch (fallbackError) {
                logger.error('Fallback message sending failed:', fallbackError);
              }
            } else {
              logger.error('Streaming failed with file attached - no fallback available');
            }
          },
        }
      );
    } catch (error) {
      logger.error('Failed to send message:', error);
      setTyping(false);
      // Não usar fallback quando há arquivo anexado
      if (!fileId) {
        // Só usar fallback se não há arquivos
        try {
          await sendQuickMessage(messageContent);

          // Se é uma nova conversa e o fallback teve sucesso, atualiza o histórico
          if (isNewConversation) {
            logger.dev('Nova conversa criada via fallback final, atualizando histórico...');
            setTimeout(() => {
              dispatch(fetchConversations({}));
            }, 1000);
          }
        } catch (fallbackError) {
          logger.error('Fallback message sending also failed:', fallbackError);
        }
      } else {
        logger.error('Message sending failed with file attached - no fallback available');
      }
    }
  };

  const handleRegenerateResponse = async () => {
    if (isLoading || isStreaming) return;

    // Find the last assistant message and the user message that prompted it
    const assistantMessages = messages.filter((m: Message) => m.role === 'assistant');
    const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
    if (!lastAssistantMessage) return;

    const assistantIndex = messages.findIndex((m: Message) => m.id === lastAssistantMessage.id);
    const userMessage = messages[assistantIndex - 1];

    if (!userMessage || userMessage.role !== 'user') return;

    if (!selectedModel) {
      logger.error('Nenhum modelo selecionado');
      return;
    }

    try {
      // Remove the last assistant message before regenerating
      if (lastMessage && lastMessage.role === 'assistant') {
        removeMessagesAfterMessage(lastMessage.id);
      }

      // Set typing state before starting regeneration
      setTyping(true);

      // Use streaming for regeneration just like regular messages
      await startStreaming(
        userMessage.content,
        {
          modelId: selectedModel.id,
          modelName: selectedModel.name,
          provider: selectedModel.provider.toLowerCase(),
          sessionId: currentConversation?.sessionId, // Use sessionId as primary identifier
          isRegeneration: false, // Now we use false since we removed the message
          onStart: () => {
            logger.mock('Regeneration streaming started', selectedModel.id);
          },
          onComplete: () => {
            logger.mock('Regeneration streaming completed');
            setTyping(false);
          },
          onError: async (error) => {
            logger.error('Regeneration streaming error:', error);
            setTyping(false);
            // Fallback to regular regeneration
            try {
              await regenerateLastMessage();
            } catch (fallbackError) {
              logger.error('Fallback regeneration failed:', fallbackError);
            }
          },
        }
      );
    } catch (error) {
      logger.error('Failed to regenerate message with streaming:', error);
      setTyping(false);
      // Fallback to regular regeneration
      try {
        await regenerateLastMessage();
      } catch (fallbackError) {
        logger.error('Fallback regeneration also failed:', fallbackError);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendOrStop();
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 200) + 'px';
    }
  };

  const handleFileSelect = () => {
    if (fileInputRef.current && !isUploadingFiles && !isStreaming && !isTyping) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      // Limitar a 1 arquivo conforme especificação do backend
      const filesToAdd = files.slice(0, 1 - attachedFiles.length);
      setAttachedFiles((prev) => [...prev, ...filesToAdd]);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileRemove = (index: number) => {
    setAttachedFiles((prev) => {
      const newFiles = prev.filter((_, i) => i !== index);
      // Se não há mais arquivos, limpar também os file_ids
      if (newFiles.length === 0) {
        setUploadedFileIds([]);
      }
      return newFiles;
    });

    // Se havia file_ids correspondentes, também remover
    if (uploadedFileIds.length > index) {
      setUploadedFileIds((prev) => prev.filter((_, i) => i !== index));
    }

    logger.dev('Arquivo removido', {
      removedIndex: index,
      remainingFiles: attachedFiles.length - 1
    });
  };

  // Função removida - agora usamos chatApi.uploadFileForChat diretamente

  // Drag & Drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCounter = prev - 1;
      if (newCounter === 0) {
        setIsDragOver(false);
      }
      return newCounter;
    });
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    if (isStreaming || isTyping) {
      return;
    }

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      // Validar tipos de arquivo
      const validTypes = ['.pdf', '.doc', '.docx', '.txt', '.md', '.jpg', '.jpeg', '.png', '.gif'];
      const validFiles = files.filter(file => {
        const extension = '.' + file.name.split('.').pop()?.toLowerCase();
        const isValidType = validTypes.includes(extension);
        const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
        return isValidType && isValidSize;
      });

      // Limitar a 1 arquivo conforme especificação do backend
      const availableSlots = 1 - attachedFiles.length;
      const filesToAdd = validFiles.slice(0, availableSlots);

      if (filesToAdd.length > 0) {
        setAttachedFiles((prev) => [...prev, ...filesToAdd]);

        // Log para debug
        logger.dev('Arquivos adicionados via drag & drop', {
          totalFiles: files.length,
          validFiles: validFiles.length,
          addedFiles: filesToAdd.length,
          fileNames: filesToAdd.map(f => f.name)
        });
      }

      // Mostrar feedback se alguns arquivos foram rejeitados
      if (validFiles.length < files.length) {
        logger.warn('Alguns arquivos foram rejeitados por tipo ou tamanho inválido');
      }
    }
  }, [isStreaming, isTyping, attachedFiles.length]);




  const handleToolSelect = (tool: Tool) => {
    logger.dev('Tool selected:', tool);
    setSelectedTool(tool);
    setShowToolsMenu(false);
  };
  const handleCopyMessage = (messageContent: string) => {
    navigator.clipboard.writeText(messageContent).then(() => {
      logger.mock('Message copied to clipboard');
    }).catch((error) => {
      logger.error('Failed to copy message:', error);
    });
  };

  const handleLikeMessage = (messageId: string) => {
    // TODO: Implementar sistema de feedback quando API estiver disponível
    logger.mock('Message liked:', messageId);
  };

  const handleDislikeMessage = (messageId: string) => {
    // TODO: Implementar sistema de feedback quando API estiver disponível
    logger.mock('Message disliked:', messageId);
  };

  const handleEditMessage = async (messageId: string, newContent: string) => {
    try {
      // Find the message being edited
      const messageIndex = messages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) return;

      const editedMessage = messages[messageIndex];

      // Update the message content
      updateChatMessage(messageId, newContent);

      // Remove all messages after the edited message (including assistant responses)
      removeMessagesAfterAction(messageId);

      // If it's a user message, send the edited content as a new message to get a new response
      if (editedMessage.role === 'user') {
        // Set typing state before starting streaming (just like in normal message flow)
        setTyping(true);

        // Wait a bit for the state to update
        setTimeout(async () => {
          if (!selectedModel) {
            logger.error('Nenhum modelo selecionado');
            setTyping(false);
            return;
          }

          try {
            await startStreaming(
              newContent,
              {
                modelId: selectedModel.id,
                modelName: selectedModel.name,
                provider: selectedModel.provider.toLowerCase(),
                sessionId: currentConversation?.sessionId, // Use sessionId as primary identifier
                isRegeneration: false,
                onStart: () => {
                  logger.mock('Streaming started for edited message');
                },
                onComplete: () => {
                  logger.mock('Streaming completed for edited message');
                  setTyping(false);
                },
                onError: (error) => {
                  logger.error('Streaming error for edited message:', error);
                  setTyping(false);
                },
              }
            );
          } catch (error) {
            logger.error('Failed to send edited message:', error);
            setTyping(false);
          }
        }, 100);
      }
    } catch (error) {
      logger.error('Failed to edit message:', error);
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Navegação por teclado no seletor de modelos
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!showModelSelector) return;

      switch (e.key) {
        case 'Escape':
          setShowModelSelector(false);
          setFocusedModelIndex(-1);
          break;
        case 'ArrowDown':
          e.preventDefault();
          setFocusedModelIndex(prev =>
            prev < availableModels.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedModelIndex(prev =>
            prev > 0 ? prev - 1 : availableModels.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedModelIndex >= 0 && availableModels[focusedModelIndex]) {
            handleModelSelect(availableModels[focusedModelIndex]);
            setShowModelSelector(false);
            setFocusedModelIndex(-1);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showModelSelector, focusedModelIndex, availableModels, handleModelSelect]);

  // Reset focused index when dropdown closes
  useEffect(() => {
    if (!showModelSelector) {
      setFocusedModelIndex(-1);
    }
  }, [showModelSelector]);

  const hasMessages = messages && Array.isArray(messages) && messages.length > 0;

  return (
    <div
      className={`h-full relative ${className}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Drag & Drop Overlay */}
      {isDragOver && (
        <div className={`absolute inset-0 z-50 backdrop-blur-sm border-2 border-dashed flex items-center justify-center ${
          isUploadingFiles || isStreaming || isTyping || attachedFiles.length >= 1
            ? 'bg-red-500/20 border-red-500'
            : 'bg-orange-500/20 border-orange-500'
        }`}>
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border border-orange-200">
            <div className="text-center">
              <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                isUploadingFiles || isStreaming || isTyping || attachedFiles.length >= 1
                  ? 'bg-gradient-to-br from-red-500 to-red-600'
                  : 'bg-gradient-to-br from-orange-500 to-red-500'
              }`}>
                {isUploadingFiles || isStreaming || isTyping || attachedFiles.length >= 1 ? (
                  <X className="w-8 h-8 text-white" />
                ) : (
                  <Paperclip className="w-8 h-8 text-white" />
                )}
              </div>

              {isUploadingFiles ? (
                <>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Upload em progresso...
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Aguarde o upload atual terminar
                  </p>
                </>
              ) : isStreaming || isTyping ? (
                <>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Chat ocupado
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Aguarde a resposta terminar
                  </p>
                </>
              ) : attachedFiles.length >= 1 ? (
                <>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Limite atingido
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Máximo 3 arquivos por mensagem
                  </p>
                </>
              ) : (
                <>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Solte os arquivos aqui
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Máximo 3 arquivos • 10MB cada
                  </p>
                  <p className="text-gray-500 text-xs mt-1">
                    PDF, DOC, TXT, MD, JPG, PNG, GIF
                  </p>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Messages Area - Now takes full height */}
      <div
        ref={messagesContainerRef}
        className="absolute inset-0 overflow-y-auto"
        style={{ paddingBottom: hasMessages ? '110px' : '160px' }}
        onScroll={handleScroll}
      >
        {hasMessages ? (
          <div className="max-w-5xl mx-auto px-6 py-8 space-y-8">
            {messages.map((message: Message, index: number) => (
              <div key={message.id}>
                <MessageBubble
                  content={message.content}
                  role={message.role as 'user' | 'assistant'}
                  timestamp={new Date(message.timestamp)}
                  model={message.model}
                  attachments={message.attachments}
                  files={message.files}
                  isStreaming={isStreaming}
                  onCopy={() => handleCopyMessage(message.content)}
                  onLike={() => handleLikeMessage(message.id)}
                  onDislike={() => handleDislikeMessage(message.id)}
                  onEdit={
                    message.role === 'user'
                      ? (newContent: string) => handleEditMessage(message.id, newContent)
                      : undefined
                  }
                  onRegenerate={
                    message.role === 'assistant' &&
                      index === messages.length - 1 &&
                      !isLoading &&
                      !isStreaming
                      ? handleRegenerateResponse
                      : undefined
                  }
                />
              </div>
            ))}

            {/* Show typing indicator first, then streaming message */}
            {(isLoading || isStreaming || isTyping) && !streamingMessage && (
              <TypingIndicator
                modelName={selectedModel?.name || 'Modelo'}
              />
            )}

            {streamingMessage && (
              <StreamingMessage
                content={String(streamingMessage || '')}
                modelName={selectedModel?.name || 'Modelo'}
              />
            )}

            <div ref={messagesEndRef} />
          </div>
        ) : (
          /* Welcome Screen */
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-2xl px-4">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-3xl flex items-center justify-center mx-auto shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <Sparkles className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-orange-400 to-red-400 rounded-full animate-pulse"></div>
              </div>

              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Cognit <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">Studio</span>
              </h1>

              <p className="text-lg text-gray-600 mb-8">
                Sua IA pessoal está pronta para ajudar
              </p>

              <div className="text-sm text-orange-600 bg-orange-50 px-6 py-3 rounded-full inline-block border border-orange-100">
                ✨ Comece digitando sua pergunta abaixo
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Floating Input Area */}
      <div className="absolute bottom-0 left-0 right-0 px-6 py-6 pointer-events-none">
        <div className="pointer-events-auto max-w-5xl mx-auto">
          {/* Main Input Container - Enhanced floating effect */}
          <div className="relative bg-white/20 backdrop-blur-xl rounded-2xl border border-white/20 focus-within:border-orange-500 focus-within:shadow-2xl focus-within:bg-white/30 transition-all duration-300 shadow-2xl hover:shadow-3xl hover:bg-white/25 hover:focus-within:border-orange-500">
            {/* File Attachments - Inside the input container */}
            {(attachedFiles.length > 0 || isUploadingFiles) && (
              <div className="px-4 pt-4 pb-2">
                {/* Upload Progress */}
                {isUploadingFiles && (
                  <div className="mb-2 p-2 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex items-center gap-2 text-sm text-orange-700">
                      <div className="animate-spin w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full"></div>
                      <span>Fazendo upload dos arquivos... {uploadProgress}%</span>
                    </div>
                    <div className="mt-1 w-full bg-orange-200 rounded-full h-1">
                      <div
                        className="bg-orange-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  {attachedFiles.map((file, index) => (
                    <div
                      key={`${file.name}-${index}`}
                      className="flex items-center gap-2 bg-white/50 backdrop-blur-sm text-gray-700 px-3 py-2 rounded-lg text-sm border border-white/30 hover:bg-white/70 hover:border-white/40 transition-all duration-200 shadow-sm"
                    >
                      <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                        {getFileIcon()}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 truncate max-w-32">{file.name}</div>
                        <div className="text-xs text-gray-500">
                          {getFileTypeLabel(file.type)} • {formatFileSize(file.size)}
                        </div>
                      </div>
                      <button
                        onClick={() => handleFileRemove(index)}
                        className="text-gray-400 hover:text-gray-600 ml-1 hover:bg-white/60 rounded-full p-1 transition-all duration-200 flex-shrink-0"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Input Area */}
            <div className="flex items-end p-4">
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Digite sua mensagem..."
                  className="w-full bg-transparent resize-none border-none outline-none text-gray-900 placeholder-gray-500 text-lg min-h-[24px] max-h-[200px]"
                  rows={1}
                />
              </div>
            </div>

            {/* Bottom Controls */}
            <div className="flex items-center justify-between px-4 pb-4">
              {/* Left side controls */}
              <div className="flex items-center space-x-2">
                {/* File Upload */}
                <button
                  onClick={handleFileSelect}
                  disabled={Boolean(streamingMessage|| isLoading || isStreaming || isTyping || isUploadingFiles)}
                  className="p-2 text-gray-400 hover:text-orange-500 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50"
                  title={isUploadingFiles ? "Fazendo upload..." : "Anexar arquivo"}
                >
                  <Paperclip className="w-5 h-5" />
                </button>

                {/* Tools Button - Preview Feature */}
                {config.ALLOW_PREVIEW_FEATURES && (
                  <div className="relative">
                    <button
                      onClick={() => setShowToolsMenu(!showToolsMenu)}
                      disabled={Boolean(isLoading)}
                      className="p-2 text-gray-400 hover:text-orange-500 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50"
                      title="Ferramentas"
                    >
                      <Wrench className="w-5 h-5" />
                    </button>

                    {/* Tools Dropdown */}
                    <ToolsDropdown
                      isOpen={showToolsMenu}
                      onClose={() => setShowToolsMenu(false)}
                      onToolSelect={handleToolSelect}
                      tools={mockTools}
                    />
                  </div>
                )}

                {/* Selected Tool Badge */}
                {selectedTool && (
                  <div className="flex items-center space-x-2 text-orange-600 px-2 py-1 rounded-md text-sm font-medium hover:bg-orange-50 transition-colors cursor-pointer">
                    <div className="w-4 h-4 flex items-center justify-center">
                      {selectedTool.icon === 'Calculator' ? <Calculator className="w-3.5 h-3.5" /> :
                        selectedTool.icon === 'Globe' ? <Globe className="w-3.5 h-3.5" /> :
                          selectedTool.icon === 'FileText' ? <FileText className="w-3.5 h-3.5" /> :
                            selectedTool.icon === 'Image' ? <Image className="w-3.5 h-3.5" /> :
                              selectedTool.icon === 'Code' ? <Code className="w-3.5 h-3.5" /> :
                                selectedTool.icon === 'BarChart3' ? <BarChart3 className="w-3.5 h-3.5" /> :
                                  selectedTool.icon === 'Database' ? <Database className="w-3.5 h-3.5" /> :
                                    <Wrench className="w-3.5 h-3.5" />}
                    </div>
                    <span>{selectedTool.name}</span>
                    <button
                      onClick={() => setSelectedTool(null)}
                      className="w-4 h-4 rounded hover:bg-orange-100 flex items-center justify-center transition-colors"
                      title="Remover ferramenta"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>

              {/* Right side controls */}
              <div className="flex items-center space-x-3">
                {/* Model Selector */}
                <div className="relative">
                  <button
                    onClick={() => setShowModelSelector(!showModelSelector)}
                    className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                  >
                    <span>{selectedModel?.name || 'Selecionar modelo'}</span>
                    <ChevronDown className={`w-3 h-3 transition-transform ${showModelSelector ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Model Dropdown */}
                  {showModelSelector && (
                    <>
                      <div className="fixed inset-0 z-40" onClick={() => setShowModelSelector(false)} />
                      <div className="absolute bottom-full left-0 mb-2 w-80 bg-white rounded-xl border border-gray-200 shadow-xl z-50 overflow-hidden animate-fade-in">
                        {/* Models List */}
                        <div className="max-h-80 overflow-y-auto">
                          <div className="p-1">
                            {availableModels.map((model, index) => {
                              const isFocused = index === focusedModelIndex;
                              const isSelected = selectedModel?.id === model.id;

                              return (
                                <button
                                  key={model.id}
                                  onClick={() => {
                                    handleModelSelect(model);
                                    setShowModelSelector(false);
                                    setFocusedModelIndex(-1);
                                  }}
                                  onMouseEnter={() => setFocusedModelIndex(index)}
                                  className={`group/model relative w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-150 ${isFocused || isSelected
                                    ? 'bg-orange-50 text-orange-700'
                                    : 'text-gray-700 hover:bg-gray-50'
                                    }`}
                                >
                                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 transition-colors ${isFocused || isSelected
                                    ? 'bg-orange-500 text-white'
                                    : 'bg-gray-100 text-gray-600'
                                    }`}>
                                    <ProviderIcon 
                                      provider={model.provider} 
                                      size="sm" 
                                      variant="monochrome" 
                                      className={isFocused || isSelected ? 'text-white' : 'text-gray-600'} 
                                    />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-sm truncate">{model.name}</div>
                                  </div>
                                </button>
                              );
                            })}
                          </div>
                        </div>

                      </div>
                    </>
                  )}
                </div>

                {/* Send/Stop Button */}
                <button
                  onClick={handleSendOrStop}
                  disabled={Boolean(
                    (!(isStreaming || streamingMessage || isTyping) && (
                      !message.trim() ||
                      isLoading
                    )) ||
                    ((isStreaming || streamingMessage || isTyping) && !canCancel)
                  )}
                  className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-2 rounded-lg hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
                  title={
                    (isStreaming || streamingMessage || isTyping)
                      ? canCancel
                        ? 'Parar geração'
                        : 'Aguarde 1 segundo para cancelar'
                      : 'Enviar mensagem'
                  }
                >
                  {(isStreaming || streamingMessage || isTyping) ? (
                    <Square className="w-5 h-5" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileChange}
            accept=".pdf,.doc,.docx,.txt,.md,.jpg,.jpeg,.png,.gif"
            className="hidden"
          />
        </div>
      </div>

      {/* Scroll to bottom button */}
      {showScrollButton && (
        <div className="absolute bottom-44 left-1/2 transform -translate-x-1/2 z-40">
          <button
            onClick={handleScrollButtonClick}
            className="bg-white/20 backdrop-blur-xl border border-white/20 hover:bg-white/30 hover:border-white/30 text-gray-600 hover:text-gray-700 p-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 active:scale-95"
          >
            <ArrowDown className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};
