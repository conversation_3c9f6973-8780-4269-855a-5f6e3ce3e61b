import { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { authApiReal } from '../../api/authApiReal';
import { shouldUseMockServer } from '../../api/mockServer';
import { Loader2, CheckCircle2, XCircle, ArrowLeft } from 'lucide-react';

export const GoogleAuthHandler = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { loginWithGoogle } = useAuth();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const hasProcessedRef = useRef(false);

  const handleGoogleCallback = useCallback(async (code: string) => {
    if (hasProcessedRef.current) return;
    hasProcessedRef.current = true;
    
    try {
      setStatus('processing');

      // Processar callback com o backend
      const authResponse = await authApiReal.handleGoogleCallback(code);

      // Salvar tokens no localStorage
      localStorage.setItem('token', authResponse.token);
      localStorage.setItem('refreshToken', authResponse.refreshToken);
      localStorage.setItem('currentUser', JSON.stringify(authResponse.user));

      // Atualizar estado global (para API real, apenas validar token atual)
      await loginWithGoogle(authResponse.token);

      setStatus('success');

      // Redirecionar para dashboard após sucesso
      setTimeout(() => {
        navigate('/');
      }, 2000);

    } catch (error) {
      console.error('Erro no callback Google:', error);
      setStatus('error');
      setErrorMessage('Erro ao processar autenticação Google');

      // Redirecionar para página de auth com erro após 3 segundos
      setTimeout(() => {
        navigate('/auth?error=callback_failed');
      }, 3000);
    }
  }, [loginWithGoogle, navigate]);

  useEffect(() => {
    // Se estiver usando mock server, redirecionar para auth normal
    if (shouldUseMockServer()) {
      navigate('/auth');
      return;
    }

    // Se já processou, não fazer nada
    if (hasProcessedRef.current) return;

    const code = searchParams.get('code');
    const error = searchParams.get('error');
    
    if (error) {
      console.error('OAuth error:', error);
      setStatus('error');
      setErrorMessage('Erro na autenticação Google: ' + error);
      
      // Redirecionar para página de auth com erro após 3 segundos
      setTimeout(() => {
        navigate('/auth?error=oauth_failed');
      }, 3000);
      return;
    }
    
    if (code) {
      handleGoogleCallback(code);
    } else {
      setStatus('error');
      setErrorMessage('Código de autorização não encontrado');
      setTimeout(() => {
        navigate('/auth?error=no_code');
      }, 3000);
    }
  }, [searchParams, navigate, handleGoogleCallback]);
  
  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return 'Processando autenticação Google...';
      case 'success':
        return 'Autenticação realizada com sucesso! Redirecionando...';
      case 'error':
        return errorMessage || 'Erro na autenticação';
      default:
        return 'Processando...';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-12 w-12 text-orange-600 animate-spin" />;
      case 'success':
        return <CheckCircle2 className="h-12 w-12 text-green-600" />;
      case 'error':
        return <XCircle className="h-12 w-12 text-red-600" />;
      default:
        return null;
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          {/* Status Icon */}
          <div className="flex justify-center mb-6">
            {getStatusIcon()}
          </div>
          
          {/* Title */}
          <h1 className="text-2xl font-bold text-gray-900 mb-3">
            Autenticação Google
          </h1>
          
          {/* Status Message */}
          <p className="text-gray-600 mb-6">
            {getStatusMessage()}
          </p>
          
          {/* Error Actions */}
          {status === 'error' && (
            <button
              onClick={() => navigate('/auth')}
              className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium rounded-xl hover:from-orange-600 hover:to-red-600 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar para login
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default GoogleAuthHandler;