import React from 'react';
import 'katex/dist/katex.min.css';
import { InlineMath, BlockMath } from 'react-katex';

export interface LaTeXRendererProps {
  content: string;
  className?: string;
}

/**
 * Componente para renderizar texto com fórmulas LaTeX
 * 
 * Suporta:
 * - Fórmulas inline: $formula$ ou \(formula\)
 * - Fórmulas em bloco: $$formula$$ ou \[formula\]
 * - Texto normal misturado com fórmulas
 */
export const LaTeXRenderer: React.FC<LaTeXRendererProps> = ({
  content,
  className = ''
}) => {
  const renderContent = (text: string) => {
    // Regex para detectar diferentes tipos de fórmulas LaTeX
    const patterns = [
      // Fórmulas em bloco: $$...$$ (com quebras de linha)
      { regex: /\$\$([\s\S]*?)\$\$/g, type: 'block' },
      // Fórmulas em bloco: \[...\] (com quebras de linha)
      { regex: /\\\[([\s\S]*?)\\\]/g, type: 'block' },
      // Fórmulas inline: $...$
      { regex: /\$(.*?)\$/g, type: 'inline' },
      // Fórmulas inline: \(...\)
      { regex: /\\\((.*?)\\\)/g, type: 'inline' },
      // Equações simples: variável = valor (ex: a = 3, x = 5)
      { regex: /\b([a-zA-Z]\w*)\s*=\s*([0-9]+(?:\.[0-9]+)?)\b/g, type: 'simple-equation' },
    ];

    let parts: Array<{ content: string; type: 'text' | 'inline' | 'block' }> = [
      { content: text, type: 'text' }
    ];

    // Processar cada padrão de fórmula
    patterns.forEach(({ regex, type }) => {
      const newParts: typeof parts = [];

      parts.forEach(part => {
        if (part.type !== 'text') {
          newParts.push(part);
          return;
        }

        const matches = Array.from(part.content.matchAll(regex));
        if (matches.length === 0) {
          newParts.push(part);
          return;
        }

        let lastIndex = 0;
        matches.forEach(match => {
          const beforeMatch = part.content.slice(lastIndex, match.index);
          if (beforeMatch) {
            newParts.push({ content: beforeMatch, type: 'text' });
          }

          if (type === 'simple-equation') {
            // Para equações simples, formatar como LaTeX inline
            newParts.push({
              content: `${match[1]} = ${match[2]}`,
              type: 'inline'
            });
          } else {
            newParts.push({
              content: match[1],
              type: type as 'inline' | 'block'
            });
          }

          lastIndex = (match.index || 0) + match[0].length;
        });

        const afterLastMatch = part.content.slice(lastIndex);
        if (afterLastMatch) {
          newParts.push({ content: afterLastMatch, type: 'text' });
        }
      });

      parts = newParts;
    });

    return parts;
  };

  const renderPart = (part: { content: string; type: 'text' | 'inline' | 'block' }, index: number) => {
    try {
      switch (part.type) {
        case 'inline':
          return (
            <InlineMath 
              key={index} 
              math={part.content.trim()}
              errorColor="#dc2626"
              renderError={(_error) => (
                <span className="text-red-600 bg-red-50 px-1 rounded text-sm">
                  LaTeX Error: {part.content}
                </span>
              )}
            />
          );
        
        case 'block':
          return (
            <div key={index} className="my-4">
              <BlockMath
                math={part.content.trim()}
                errorColor="#dc2626"
                renderError={(_error) => (
                  <div className="text-red-600 bg-red-50 p-2 rounded text-sm border border-red-200">
                    LaTeX Error: {part.content}
                  </div>
                )}
              />
            </div>
          );
        
        case 'text':
        default: {
          // Preservar quebras de linha no texto normal e renderizar listas com bolinhas
          const lines = part.content.split('\n');
          return lines.map((line, lineIndex) => {
            // Detectar linhas que começam com hífen (listas)
            const listItemMatch = line.match(/^(\s*)-\s+(.+)$/);
            if (listItemMatch) {
              const [, indent, content] = listItemMatch;
              const indentLevel = Math.floor(indent.length / 2); // 2 espaços = 1 nível
              return (
                <div key={`${index}-${lineIndex}`} className="flex items-start my-1" style={{ marginLeft: `${indentLevel * 1.5}rem` }}>
                  <span className="inline-block w-2 h-2 bg-gray-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span className="flex-1">{content}</span>
                </div>
              );
            }

            // Linha normal
            return (
              <React.Fragment key={`${index}-${lineIndex}`}>
                {line}
                {lineIndex < lines.length - 1 && <br />}
              </React.Fragment>
            );
          });
        }
      }
    } catch (_error) {
      // Fallback em caso de erro na renderização
      return (
        <span key={index} className="text-red-600 bg-red-50 px-1 rounded text-sm">
          LaTeX Error: {part.content}
        </span>
      );
    }
  };

  const parts = renderContent(content);

  return (
    <div className={`latex-content ${className}`}>
      {parts.map(renderPart)}
    </div>
  );
};


