import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { LaTeXRenderer, useHasLaTeX } from '../LaTeXRenderer';
import { renderHook } from '@testing-library/react';

// Mock KaTeX para evitar problemas nos testes
vi.mock('react-katex', () => ({
  InlineMath: ({ math }: { math: string }) => <span data-testid="inline-math">{math}</span>,
  BlockMath: ({ math }: { math: string }) => <div data-testid="block-math">{math}</div>,
}));

describe('LaTeXRenderer', () => {
  it('should render plain text without LaTeX', () => {
    const content = 'This is plain text without any formulas.';
    render(<LaTeXRenderer content={content} />);
    
    expect(screen.getByText(content)).toBeInTheDocument();
    expect(screen.queryByTestId('inline-math')).not.toBeInTheDocument();
    expect(screen.queryByTestId('block-math')).not.toBeInTheDocument();
  });

  it('should render inline LaTeX formulas with $...$', () => {
    const content = 'The formula is $E = mc^2$ which is famous.';
    render(<LaTeXRenderer content={content} />);

    expect(screen.getByText(/The formula is/)).toBeInTheDocument();
    expect(screen.getByText(/which is famous/)).toBeInTheDocument();
    expect(screen.getByTestId('inline-math')).toBeInTheDocument();
    expect(screen.getByTestId('inline-math')).toHaveTextContent('E = mc^2');
  });

  it('should render block LaTeX formulas with $$...$$', () => {
    const content = 'Here is a formula:\n\n$$x = \\frac{-b}{2a}$$\n\nThat was it.';
    render(<LaTeXRenderer content={content} />);

    expect(screen.getByText(/Here is a formula/)).toBeInTheDocument();
    expect(screen.getByText(/That was it/)).toBeInTheDocument();
    expect(screen.getByTestId('block-math')).toBeInTheDocument();
    expect(screen.getByTestId('block-math')).toHaveTextContent('x = \\frac{-b}{2a}');
  });

  it('should render inline LaTeX formulas with \\(...\\)', () => {
    const content = 'The area is \\(A = \\pi r^2\\) for a circle.';
    render(<LaTeXRenderer content={content} />);

    expect(screen.getByText(/The area is/)).toBeInTheDocument();
    expect(screen.getByText(/for a circle/)).toBeInTheDocument();
    expect(screen.getByTestId('inline-math')).toBeInTheDocument();
    expect(screen.getByTestId('inline-math')).toHaveTextContent('A = \\pi r^2');
  });

  it('should render block LaTeX formulas with \\[...\\]', () => {
    const content = 'Formula:\n\n\\[\\sum_{i=1}^n i = \\frac{n(n+1)}{2}\\]\n\nEnd.';
    render(<LaTeXRenderer content={content} />);

    expect(screen.getByText(/Formula/)).toBeInTheDocument();
    expect(screen.getByText(/End/)).toBeInTheDocument();
    expect(screen.getByTestId('block-math')).toBeInTheDocument();
    expect(screen.getByTestId('block-math')).toHaveTextContent('\\sum_{i=1}^n i = \\frac{n(n+1)}{2}');
  });

  it('should handle multiple inline formulas', () => {
    const content = 'We have $a = 1$ and $b = 2$ and $c = a + b$.';
    render(<LaTeXRenderer content={content} />);
    
    const mathElements = screen.getAllByTestId('inline-math');
    expect(mathElements).toHaveLength(3);
    expect(mathElements[0]).toHaveTextContent('a = 1');
    expect(mathElements[1]).toHaveTextContent('b = 2');
    expect(mathElements[2]).toHaveTextContent('c = a + b');
  });

  it('should preserve line breaks in text', () => {
    const content = 'Line 1\nLine 2\nLine 3';
    const { container } = render(<LaTeXRenderer content={content} />);

    // Verificar se as quebras de linha são preservadas
    expect(screen.getByText(/Line 1/)).toBeInTheDocument();
    expect(screen.getByText(/Line 2/)).toBeInTheDocument();
    expect(screen.getByText(/Line 3/)).toBeInTheDocument();
    expect(container.innerHTML).toContain('<br>');
  });

  it('should apply custom className', () => {
    const content = 'Test content';
    const customClass = 'custom-latex-class';
    
    const { container } = render(<LaTeXRenderer content={content} className={customClass} />);
    
    expect(container.firstChild).toHaveClass('latex-content');
    expect(container.firstChild).toHaveClass(customClass);
  });
});

describe('useHasLaTeX', () => {
  it('should return false for plain text', () => {
    const { result } = renderHook(() => useHasLaTeX('This is plain text'));
    expect(result.current).toBe(false);
  });

  it('should return true for inline LaTeX with $...$', () => {
    const { result } = renderHook(() => useHasLaTeX('Formula: $E = mc^2$'));
    expect(result.current).toBe(true);
  });

  it('should return true for block LaTeX with $$...$$', () => {
    const { result } = renderHook(() => useHasLaTeX('Formula:\n$$x = 1$$'));
    expect(result.current).toBe(true);
  });

  it('should return true for inline LaTeX with \\(...\\)', () => {
    const { result } = renderHook(() => useHasLaTeX('Formula: \\(a = b\\)'));
    expect(result.current).toBe(true);
  });

  it('should return true for block LaTeX with \\[...\\]', () => {
    const { result } = renderHook(() => useHasLaTeX('Formula:\n\\[x = y\\]'));
    expect(result.current).toBe(true);
  });

  it('should return false for escaped dollar signs', () => {
    const { result } = renderHook(() => useHasLaTeX('Price: \\$100'));
    expect(result.current).toBe(false);
  });
});
