import React, { useState, useEffect } from 'react';
import { StudioChatInterface } from '../organisms/StudioChatInterface';
import { StudioKnowledgeModal } from '../organisms/StudioKnowledgeModal';
import { StudioSidebar } from '../organisms/StudioSidebar';
import { StudioHeader } from '../organisms/StudioHeader';
import { useChatConversations } from '../../hooks/useChatConversations';
import type { Conversation } from '../organisms/ConversationList';

export const StudioTemplate: React.FC = () => {
  // Redux hooks
  const {
    currentConversation,
    createNewConversation: startNewConversation,
    selectConversation: setConversation,
    isStreaming,
    isTyping,
    isLoading,
    conversations,
    removeConversation,
    loadConversations,
    updateConversationTitleAction,
  } = useChatConversations();

  // Local UI state
  const [showKnowledgeModal, setShowKnowledgeModal] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Generate client_id when opening main screen and cleanup on unmount
  useEffect(() => {
    // Generate client_id when component mounts
    const clientId = `frontend_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    localStorage.setItem('clientId', clientId);
    console.log('Client ID generated:', clientId);

    // Load conversations
    loadConversations();

    // Cleanup client_id when component unmounts (page closes)
    return () => {
      localStorage.removeItem('clientId');
      console.log('Client ID cleared');
    };
  }, [loadConversations]);

  const createNewConversation = () => {
    // Não permitir criar nova conversa durante streaming
    if (isStreaming || isTyping || isLoading) {
      return;
    }
    startNewConversation();
  };

  const handleDeleteConversation = (sessionId: string) => {
    removeConversation(sessionId);
    if (currentConversation?.sessionId === sessionId) {
      const remaining = conversations.filter((c) => c.sessionId !== sessionId);
      setConversation(remaining.length > 0 ? remaining[0] : null);
    }
  };

  const handleConversationSelect = (conversationId: string) => {
    // O conversationId é na verdade o sessionId (mapeado como id no formattedConversations)
    const conversation = conversations.find(c => c.sessionId === conversationId);
    if (conversation) {
      setConversation(conversation);
    }
  };

  // Converter conversas para o formato esperado pelo ConversationList
  const formattedConversations: Conversation[] = conversations.map(conv => ({
    id: conv.sessionId,
    title: conv.title,
    lastMessage: conv.messages[conv.messages.length - 1]?.content || '',
    timestamp: new Date(conv.updatedAt),
    model: conv.model,
    messageCount: conv.messages.length,
  }));

  return (
    <div className="h-full flex flex-col bg-gray-50 overflow-hidden">
      {/* Cabeçalho Global */}
      <StudioHeader
        onKnowledgeBaseClick={() => setShowKnowledgeModal(true)}
      />

      {/* Layout Principal */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar com Histórico */}
        <StudioSidebar
          conversations={formattedConversations}
          currentConversationId={currentConversation?.sessionId}
          onConversationSelect={handleConversationSelect}
          onConversationDelete={handleDeleteConversation}
          onConversationRename={updateConversationTitleAction}
          onNewConversation={createNewConversation}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          isLoading={isLoading}
        />

        {/* Área Principal do Chat */}
        <div className="flex-1 bg-white">
          <StudioChatInterface />
        </div>
      </div>

      {/* Knowledge Base Modal */}
      <StudioKnowledgeModal
        isOpen={showKnowledgeModal}
        onClose={() => setShowKnowledgeModal(false)}
      />
    </div>
  );
};