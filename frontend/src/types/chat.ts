// Tipos consolidados para Chat e Conversas
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  conversationId: string;
  provider?: string;
  model?: string;
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
  cost?: number;
  attachments?: MessageAttachment[];
  files?: MessageFile[]; // Arquivos do backend (JSON com name e mime_type)
  feedback?: MessageFeedback;
  metadata?: MessageMetadata;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}

export interface MessageFile {
  name: string;
  mime_type: string;
}

export interface MessageFeedback {
  liked: boolean | null;
  rating?: number;
  comment?: string;
  createdAt: string;
}

export interface MessageMetadata {
  processingTime?: number;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  stopSequences?: string[];
}

export interface Conversation {
  id: string;
  sessionId: string; // ID principal usado pelo backend
  title: string;
  messages: Message[];
  provider: string;
  model: string;
  createdAt: string;
  updatedAt: string;
  isFavorite: boolean;
  tags: string[];
  summary?: string;
  totalTokens: number;
  totalCost: number;
  lastMessageAt: string;
  messageCount: number;
}

// Requests
export interface SendMessageRequest {
  sessionId?: string;
  content: string;
  provider: string;
  model: string;
  files?: File[];
  stream?: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  stopSequences?: string[];
}

export interface StreamMessageRequest extends SendMessageRequest {
  stream: true;
}

export interface CreateConversationRequest {
  title?: string;
  provider: string;
  model: string;
  firstMessage?: string;
}

export interface UpdateConversationRequest {
  title?: string;
  isFavorite?: boolean;
  tags?: string[];
}

export interface SearchConversationsRequest {
  query: string;
  limit?: number;
  offset?: number;
  provider?: string;
  model?: string;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
  isFavorite?: boolean;
}

// Responses
export interface ConversationsListResponse {
  conversations: Conversation[];
  total: number;
  hasMore: boolean;
}

// Streaming
export interface StreamingResponse {
  id: string;
  type: 'start' | 'content' | 'end' | 'error';
  content?: string;
  metadata?: Record<string, unknown>;
  error?: string;
}

// Filters
export interface ConversationFilters {
  provider?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  hasFiles?: boolean;
  isFavorite?: boolean;
}

// Estado consolidado para Chat + Conversas
export interface ChatConversationsState {
  // Lista de conversas
  conversations: Conversation[];
  
  // Conversa ativa
  currentConversation: Conversation | null;
  
  // Mensagens da conversa ativa (sincronizado com currentConversation.messages)
  messages: Message[];
  
  // Estado de UI
  isLoading: boolean;
  isTyping: boolean;
  streamingMessage: string | null;
  error: string | null;
  
  // Seleções do usuário
  selectedProvider: string;
  selectedModel: string;
  
  // Filtros e busca
  searchQuery: string;
  filters: ConversationFilters;
  favoriteConversations: string[];
  
  // Paginação
  hasMore: boolean;
  
  // Estado temporário
  pendingSessionId: string | null;
}
