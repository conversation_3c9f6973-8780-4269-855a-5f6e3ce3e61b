@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent horizontal scrollbars */
html, body {
  overflow-x: hidden;
}

/* Ensure root container takes full height */
#root {
  height: 100vh;
  overflow-x: hidden;
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* Modal animations */
@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-zoom-in {
  from {
    opacity: 0;
    transform: scale(0.98) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-in {
  animation: modal-fade-in 0.15s ease-out;
}

.fade-in-0 {
  animation: modal-fade-in 0.15s ease-out;
}

.zoom-in-95 {
  animation: modal-zoom-in 0.2s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #fed7aa;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #fb923c;
}
/* Remove default button focus outline */
button:focus {
  outline: none;
}

/* Custom focus styles for accessibility */
button:focus-visible {
  outline: 2px solid #fb923c;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
button, [role="button"] {
  transition: all 0.2s ease-out;
}

/* Tooltip animations */
@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.tooltip-enter {
  animation: tooltip-fade-in 0.2s ease-out;
}
/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Sidebar navigation item height consistency */
.sidebar-nav-item {
  min-height: 3.5rem;
}

.sidebar-nav-item-collapsed {
  height: 3rem;
}

/* Ensure consistent text rendering */
.sidebar-text {
  line-height: 1.2;
}