import { useState, useCallback, useRef } from 'react';
import { chatApi } from '../api/chatApi';
import { logger } from '../shared/utils/logger';
import type { StreamMessageRequest, StreamingResponse } from '../types/chat';

interface UseChatStreamingOptions {
  onMessage?: (content: string) => void;
  onComplete?: (fullMessage: string) => void;
  onError?: (error: Error) => void;
  onSessionCreated?: (sessionId: string) => void;
  fileIds?: string[];
}

export const useChatStreaming = (options: UseChatStreamingOptions = {}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [error, setError] = useState<Error | null>(null);
  const cancelFunctionRef = useRef<(() => void) | null>(null);

  const startStreaming = useCallback((request: StreamMessageRequest) => {
    if (isStreaming) {
      logger.warn('Tentativa de iniciar streaming enquanto já está ativo');
      return;
    }

    setIsStreaming(true);
    setStreamingContent('');
    setError(null);

    // Log removido para reduzir poluição do console

    let fullContent = '';

    const cancelFunction = chatApi.sendStreamMessage(
      request,
      (data: StreamingResponse) => {
        if (data.type === 'start') {
          // Início do streaming - não há ação específica necessária
        } else if (data.type === 'content' && data.content) {
          fullContent += data.content;
          setStreamingContent(fullContent);
          options.onMessage?.(data.content);
        } else if (data.type === 'end') {
          setIsStreaming(false);

          // Se a mensagem de fim tem conteúdo (caso de interrupção), usar esse conteúdo
          // Caso contrário, usar o fullContent acumulado
          const finalContent = data.content || fullContent;

          // Atualizar o conteúdo final se for diferente do que já temos
          if (finalContent !== fullContent) {
            setStreamingContent(finalContent);
            fullContent = finalContent;
          }

          options.onComplete?.(finalContent);
        }
      },
      (error: Error) => {
        logger.error('Erro no streaming', error);
        setError(error);
        setIsStreaming(false);
        options.onError?.(error);
      },
      () => {
        setIsStreaming(false);
        if (fullContent) {
          options.onComplete?.(fullContent);
        }
      },
      options.onSessionCreated,
      options.fileIds
    );

    cancelFunctionRef.current = cancelFunction;
  }, [isStreaming, options]);

  const stopStreaming = useCallback(() => {
    if (cancelFunctionRef.current) {
      logger.dev('Parando streaming manualmente');
      cancelFunctionRef.current();
      cancelFunctionRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  const clearContent = useCallback(() => {
    setStreamingContent('');
    setError(null);
  }, []);

  return {
    isStreaming,
    streamingContent,
    error,
    startStreaming,
    stopStreaming,
    clearContent,
  };
};

export default useChatStreaming;