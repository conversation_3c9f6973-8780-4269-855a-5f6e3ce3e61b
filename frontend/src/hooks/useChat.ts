// Hook descontinuado - use useChatConversations em vez disso
import { useChatConversations } from './useChatConversations';

/**
 * @deprecated Use useChatConversations instead
 * This hook is maintained for backward compatibility
 */
export const useChat = () => {
  const chatConversations = useChatConversations();
  
  return {
    // State - mapped from new hook
    currentConversation: chatConversations.currentConversation,
    messages: chatConversations.messages,
    isTyping: chatConversations.isTyping,
    selectedProvider: chatConversations.selectedProvider,
    selectedModel: chatConversations.selectedModel,
    streamingMessage: chatConversations.streamingMessage,
    isLoading: chatConversations.isLoading,
    error: chatConversations.error,
    lastMessage: chatConversations.lastMessage,
    messageCount: chatConversations.messageCount,
    
    // Computed
    hasMessages: chatConversations.hasMessages,
    canRegenerate: chatConversations.canRegenerate,
    isStreaming: chatConversations.isStreaming,
    
    // Actions - mapped from new hook
    sendChatMessage: chatConversations.sendChatMessage,
    sendQuickMessage: chatConversations.sendQuickMessage,
    uploadChatFile: chatConversations.uploadChatFile,
    setConversation: chatConversations.selectConversation,
    changeProvider: chatConversations.changeProvider,
    changeModel: chatConversations.changeModel,
    setTyping: chatConversations.setTyping,
    setStreaming: chatConversations.setStreaming,
    addNewMessage: chatConversations.addNewMessage,
    updateChatMessage: chatConversations.updateChatMessage,
    removeMessagesAfterMessage: chatConversations.removeMessageAction,
    clearChatMessages: () => chatConversations.createNewConversation(),
    clearChatError: chatConversations.clearChatError,
    regenerateLastMessage: chatConversations.regenerateLastMessage,
    startNewConversation: chatConversations.createNewConversation,
  };
};