// Hook descontinuado - use useChatConversations em vez disso
import { useChatConversations } from './useChatConversations';

/**
 * @deprecated Use useChatConversations instead
 * This hook is maintained for backward compatibility
 */
export const useConversations = () => {
  const chatConversations = useChatConversations();
  
  return {
    // State
    conversations: chatConversations.conversations,
    favoriteConversations: chatConversations.favoriteConversations,
    searchQuery: chatConversations.searchQuery,
    isLoading: chatConversations.isLoading,
    hasMore: chatConversations.hasMore,
    filters: chatConversations.filters,
    filteredConversations: chatConversations.filteredConversations,
    recentConversations: chatConversations.recentConversations,
    favoriteConversationsList: chatConversations.favoriteConversationsList,
    conversationsByProvider: chatConversations.conversationsByProvider,
    
    // Computed
    hasConversations: chatConversations.hasConversations,
    hasFavorites: chatConversations.hasFavorites,
    hasActiveFilters: chatConversations.hasActiveFilters,
    filteredCount: chatConversations.filteredCount,
    
    // Actions
    loadConversations: chatConversations.loadConversations,
    loadConversation: chatConversations.loadConversation,
    removeConversation: chatConversations.removeConversation,
    updateConversationTitleAction: chatConversations.updateConversationTitleAction,
    updateSearchQuery: chatConversations.updateSearchQuery,
    updateFilters: chatConversations.updateFilters,
    toggleConversationFavorite: chatConversations.toggleConversationFavorite,
    resetFilters: chatConversations.resetFilters,
    
    // Helpers
    getConversationById: chatConversations.getConversationById,
    isConversationFavorite: chatConversations.isConversationFavorite,
    searchConversations: chatConversations.searchConversationsLocal,
    filterByProvider: chatConversations.filterByProvider,
    filterByDateRange: chatConversations.filterByDateRange,
    filterByFiles: chatConversations.filterByFiles,
  };
};