import { useState, useCallback } from 'react';

export interface ConfirmationConfig {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  icon?: React.ReactNode;
}

export interface ConfirmationState extends ConfirmationConfig {
  isOpen: boolean;
  loading: boolean;
  onConfirm: () => void | Promise<void>;
}

export const useConfirmation = () => {
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>({
    isOpen: false,
    loading: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });

  const showConfirmation = useCallback((
    config: ConfirmationConfig,
    onConfirm: () => void | Promise<void>
  ) => {
    setConfirmationState({
      ...config,
      isOpen: true,
      loading: false,
      onConfirm,
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    setConfirmationState(prev => ({
      ...prev,
      isOpen: false,
      loading: false,
    }));
  }, []);

  const handleConfirm = useCallback(async () => {
    const currentOnConfirm = confirmationState.onConfirm;

    setConfirmationState(prev => ({
      ...prev,
      loading: true,
    }));

    try {
      await currentOnConfirm();
      hideConfirmation();
    } catch (error) {
      // Em caso de erro, mantém o modal aberto e remove o loading
      setConfirmationState(prev => ({
        ...prev,
        loading: false,
      }));
      throw error; // Re-throw para permitir tratamento no componente
    }
  }, [confirmationState.onConfirm, hideConfirmation]);

  return {
    confirmationState,
    showConfirmation,
    hideConfirmation,
    handleConfirm,
  };
};
