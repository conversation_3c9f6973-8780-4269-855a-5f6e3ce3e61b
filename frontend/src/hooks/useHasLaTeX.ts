/**
 * Hook para detectar se um texto contém fórmulas LaTeX
 */
export const useHasLaTeX = (content: string): boolean => {
  const latexPatterns = [
    /\$\$[\s\S]*?\$\$/g,     // $$...$$ (with line breaks)
    /\\\[[\s\S]*?\\\]/g,     // \[...\] (with line breaks)
    /\$.*?\$/g,              // $...$
    /\\\(.*?\\\)/g,          // \(...\)
    /\b([a-zA-Z]\w*)\s*=\s*([0-9]+(?:\.[0-9]+)?)\b/g, // simple equations like a = 3
  ];

  return latexPatterns.some(pattern => pattern.test(content));
};
