import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { sendMessage, uploadFile } from './chatActions';
import type { ChatState } from './chatTypes';
import type { Message, Conversation } from '../../api/chatApi';

const initialState: ChatState = {
  currentConversation: null,
  messages: [],
  isTyping: false,
  selectedProvider: 'openai',
  selectedModel: 'gpt-4-turbo',
  streamingMessage: null,
  isLoading: false,
  error: null,
  pendingSessionId: null, // Armazenar sessionId temporariamente
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setCurrentConversation: (state, action: PayloadAction<Conversation | null>) => {
      const newConversation = action.payload;

      console.log('🔄 Redux setCurrentConversation:', {
        currentSessionId: state.currentConversation?.sessionId,
        newSessionId: newConversation?.sessionId,
        currentMessagesCount: state.messages.length,
        newMessagesCount: newConversation?.messages?.length || 0
      });

      // Se é uma nova conversa sendo criada durante streaming e já temos mensagens no estado atual,
      // preservar as mensagens existentes (apenas para novas conversas sem mensagens próprias)
      if (newConversation &&
          newConversation.sessionId &&
          state.messages.length > 0 &&
          (!state.currentConversation?.sessionId || state.currentConversation.sessionId !== newConversation.sessionId) &&
          (!newConversation.messages || newConversation.messages.length === 0)) {

        console.log('🔄 Preservando mensagens atuais para nova conversa');
        // Nova conversa sem mensagens próprias, mas já temos mensagens no estado - preservar mensagens atuais
        state.currentConversation = {
          ...newConversation,
          messages: state.messages // Manter mensagens atuais
        };
        // Não alterar state.messages pois já estão corretas
      } else {
        console.log('🔄 Definindo conversa normalmente');
        // Caso normal - definir conversa e suas mensagens (incluindo seleção de conversas existentes)
        state.currentConversation = newConversation;
        state.messages = newConversation?.messages || [];
      }
    },
    setSelectedProvider: (state, action: PayloadAction<string>) => {
      state.selectedProvider = action.payload;
    },
    setSelectedModel: (state, action: PayloadAction<string>) => {
      state.selectedModel = action.payload;
    },
    setIsTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    setStreamingMessage: (state, action: PayloadAction<string | null>) => {
      state.streamingMessage = action.payload;
      // Se começou a receber streaming, não está mais "typing"
      if (action.payload) {
        state.isTyping = false;
      }
    },
    clearStreamingMessage: (state) => {
      state.streamingMessage = null;
    },
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    replaceLastAssistantMessage: (state, action: PayloadAction<Message>) => {
      // Find the last assistant message and replace it
      let lastAssistantIndex = -1;
      for (let i = state.messages.length - 1; i >= 0; i--) {
        if (state.messages[i].role === 'assistant') {
          lastAssistantIndex = i;
          break;
        }
      }

      if (lastAssistantIndex !== -1) {
        state.messages[lastAssistantIndex] = action.payload;
      } else {
        // If no assistant message found, add as new message
        state.messages.push(action.payload);
      }
    },
    removeLastAssistantMessage: (state) => {
      // Find and remove the last assistant message
      let lastAssistantIndex = -1;
      for (let i = state.messages.length - 1; i >= 0; i--) {
        if (state.messages[i].role === 'assistant') {
          lastAssistantIndex = i;
          break;
        }
      }

      if (lastAssistantIndex !== -1) {
        state.messages.splice(lastAssistantIndex, 1);
      }
    },
    updateMessage: (state, action: PayloadAction<{ messageId: string; content: string }>) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.messageId);
      if (messageIndex !== -1) {
        state.messages[messageIndex] = {
          ...state.messages[messageIndex],
          content: action.payload.content,
        };
      }
    },
    removeMessagesAfter: (state, action: PayloadAction<string>) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload);
      if (messageIndex !== -1) {
        state.messages = state.messages.slice(0, messageIndex + 1);
      }
    },
    clearMessages: (state) => {
      state.messages = [];
      state.currentConversation = null;
      state.pendingSessionId = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setPendingSessionId: (state, action: PayloadAction<string | null>) => {
      state.pendingSessionId = action.payload;
    },
    applyPendingSessionId: (state) => {
      if (state.pendingSessionId && !state.currentConversation?.sessionId) {
        // Criar nova conversa com o sessionId se não existir
        if (!state.currentConversation) {
          state.currentConversation = {
            id: state.pendingSessionId,
            sessionId: state.pendingSessionId,
            title: 'Nova conversa',
            messages: [],
            provider: 'openai',
            model: 'gpt-4o',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isFavorite: false,
            tags: [],
            totalTokens: 0,
            totalCost: 0,
            lastMessageAt: new Date().toISOString(),
            messageCount: 0
          };
        } else {
          // Atualizar conversa existente com sessionId
          state.currentConversation.sessionId = state.pendingSessionId;
          state.currentConversation.id = state.pendingSessionId;
        }
        state.pendingSessionId = null;
      }
    },
  },
  extraReducers: (builder) => {
    // Send Message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isLoading = true;
        state.isTyping = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isTyping = false;
        state.messages.push(action.payload);
        state.error = null;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isLoading = false;
        state.isTyping = false;
        state.error = action.payload || 'Failed to send message';
      });

    // Upload File
    builder
      .addCase(uploadFile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(uploadFile.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(uploadFile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to upload file';
      });
  },
});

export const {
  setCurrentConversation,
  setSelectedProvider,
  setSelectedModel,
  setIsTyping,
  setStreamingMessage,
  clearStreamingMessage,
  addMessage,
  updateMessage,
  removeMessagesAfter,
  replaceLastAssistantMessage,
  removeLastAssistantMessage,
  clearMessages,
  clearError,
  setPendingSessionId,
  applyPendingSessionId,
} = chatSlice.actions;

export const chatReducer = chatSlice.reducer;
