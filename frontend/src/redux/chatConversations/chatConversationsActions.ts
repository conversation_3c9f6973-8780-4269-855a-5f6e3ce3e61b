import { createAsyncThunk } from '@reduxjs/toolkit';
import { chatApi } from '../../api/chatApi';
import type {
  SendMessageRequest,
  Message,
  Conversation,
  ConversationsListResponse,
  SearchConversationsRequest
} from '../../types/chat';

// Ações para mensagens
export const sendMessage = createAsyncThunk<
  Message,
  SendMessageRequest,
  { rejectValue: string }
>(
  'chatConversations/sendMessage',
  async (request, { rejectWithValue }) => {
    try {
      const response = await chatApi.sendMessage(request);
      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      return rejectWithValue(errorMessage);
    }
  }
);

export const uploadFile = createAsyncThunk<
  { url: string; id: string },
  File,
  { rejectValue: string }
>(
  'chatConversations/uploadFile',
  async (file, { rejectWithValue }) => {
    try {
      const response = await chatApi.uploadFile(file);
      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
      return rejectWithValue(errorMessage);
    }
  }
);

// Ações para conversas
export const fetchConversations = createAsyncThunk<
  ConversationsListResponse,
  { limit?: number; offset?: number; search?: string },
  { rejectValue: string }
>(
  'chatConversations/fetchConversations',
  async ({ limit = 50, offset = 0, search }, { rejectWithValue }) => {
    try {
      const response = await chatApi.getConversations(limit, offset, search);
      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch conversations';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchConversation = createAsyncThunk<
  Conversation,
  string,
  { rejectValue: string }
>(
  'chatConversations/fetchConversation',
  async (id, { rejectWithValue }) => {
    try {
      const conversation = await chatApi.getConversation(id);
      return conversation;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch conversation';
      return rejectWithValue(errorMessage);
    }
  }
);

export const deleteConversation = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>(
  'chatConversations/deleteConversation',
  async (id, { rejectWithValue }) => {
    try {
      await chatApi.deleteConversation(id);
      return id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete conversation';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateConversationTitle = createAsyncThunk<
  { id: string; title: string },
  { id: string; title: string },
  { rejectValue: string }
>(
  'chatConversations/updateConversationTitle',
  async ({ id, title }, { rejectWithValue }) => {
    try {
      await chatApi.updateConversation(id, { title });
      return { id, title };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update conversation title';
      return rejectWithValue(errorMessage);
    }
  }
);

export const searchConversations = createAsyncThunk<
  ConversationsListResponse,
  SearchConversationsRequest,
  { rejectValue: string }
>(
  'chatConversations/searchConversations',
  async (request, { rejectWithValue }) => {
    try {
      const response = await chatApi.searchConversations(request);
      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search conversations';
      return rejectWithValue(errorMessage);
    }
  }
);
