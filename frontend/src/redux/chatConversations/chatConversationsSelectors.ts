import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../../types';

// Base selector
export const selectChatConversationsState = (state: RootState) => state.chatConversations;

// Conversas
export const selectConversations = createSelector(
  [selectChatConversationsState],
  (state) => state.conversations
);

export const selectCurrentConversation = createSelector(
  [selectChatConversationsState],
  (state) => state.currentConversation
);

export const selectConversationById = createSelector(
  [selectConversations, (state: RootState, sessionId: string) => sessionId],
  (conversations, sessionId) => conversations.find(c => c.sessionId === sessionId)
);

// Mensagens
export const selectMessages = createSelector(
  [selectChatConversationsState],
  (state) => state.messages
);

export const selectLastMessage = createSelector(
  [selectMessages],
  (messages) => messages[messages.length - 1] || null
);

export const selectMessageCount = createSelector(
  [selectMessages],
  (messages) => messages.length
);

// Estado de UI
export const selectIsLoading = createSelector(
  [selectChatConversationsState],
  (state) => state.isLoading
);

export const selectIsTyping = createSelector(
  [selectChatConversationsState],
  (state) => state.isTyping
);

export const selectStreamingMessage = createSelector(
  [selectChatConversationsState],
  (state) => state.streamingMessage
);

export const selectError = createSelector(
  [selectChatConversationsState],
  (state) => state.error
);

// Seleções do usuário
export const selectSelectedProvider = createSelector(
  [selectChatConversationsState],
  (state) => state.selectedProvider
);

export const selectSelectedModel = createSelector(
  [selectChatConversationsState],
  (state) => state.selectedModel
);

// Busca e filtros
export const selectSearchQuery = createSelector(
  [selectChatConversationsState],
  (state) => state.searchQuery
);

export const selectFilters = createSelector(
  [selectChatConversationsState],
  (state) => state.filters
);

export const selectFavoriteConversations = createSelector(
  [selectChatConversationsState],
  (state) => state.favoriteConversations
);

export const selectHasMore = createSelector(
  [selectChatConversationsState],
  (state) => state.hasMore
);

// Seletores computados
export const selectFilteredConversations = createSelector(
  [selectConversations, selectSearchQuery, selectFilters],
  (conversations, searchQuery, filters) => {
    let filtered = conversations;

    // Aplicar busca
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(c => 
        c.title.toLowerCase().includes(query) ||
        c.messages.some(m => m.content.toLowerCase().includes(query))
      );
    }

    // Aplicar filtro de provider
    if (filters.provider) {
      filtered = filtered.filter(c => c.provider === filters.provider);
    }

    // Aplicar filtro de data
    if (filters.dateRange) {
      filtered = filtered.filter(c => {
        const conversationDate = new Date(c.createdAt);
        return conversationDate >= filters.dateRange!.start && 
               conversationDate <= filters.dateRange!.end;
      });
    }

    // Aplicar filtro de arquivos
    if (filters.hasFiles) {
      filtered = filtered.filter(c => 
        c.messages.some(m => m.attachments && m.attachments.length > 0)
      );
    }

    // Aplicar filtro de favoritos
    if (filters.isFavorite) {
      filtered = filtered.filter(c => c.isFavorite);
    }

    return filtered;
  }
);

export const selectFavoriteConversationsList = createSelector(
  [selectConversations, selectFavoriteConversations],
  (conversations, favoriteIds) => 
    conversations.filter(c => favoriteIds.includes(c.sessionId))
);

export const selectRecentConversations = createSelector(
  [selectConversations],
  (conversations) => 
    [...conversations]
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10)
);

export const selectConversationsByProvider = createSelector(
  [selectConversations],
  (conversations) => {
    const grouped: Record<string, typeof conversations> = {};
    conversations.forEach(c => {
      if (!grouped[c.provider]) {
        grouped[c.provider] = [];
      }
      grouped[c.provider].push(c);
    });
    return grouped;
  }
);

// Estado temporário
export const selectPendingSessionId = createSelector(
  [selectChatConversationsState],
  (state) => state.pendingSessionId
);

// Computados para UI
export const selectHasMessages = createSelector(
  [selectMessageCount],
  (count) => count > 0
);

export const selectCanRegenerate = createSelector(
  [selectLastMessage],
  (lastMessage) => lastMessage?.role === 'assistant'
);

export const selectIsStreaming = createSelector(
  [selectStreamingMessage],
  (streamingMessage) => !!streamingMessage
);

export const selectHasActiveFilters = createSelector(
  [selectSearchQuery, selectFilters],
  (searchQuery, filters) => searchQuery.length > 0 || Object.keys(filters).length > 0
);
