import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';
import { authReducer } from './auth/authReducer';
import { chatConversationsReducer } from './chatConversations/chatConversationsReducer';
import { workspacesReducer } from './workspaces/workspacesReducer';
import { uiReducer } from './ui/uiReducer';
import agentsReducer from './agents/agentsReducer';
import workflowsReducer from './workflows/workflowsReducer';
import type { RootState } from '../types';

// Combine reducers
const rootReducer = combineReducers({
  auth: authReducer,
  chatConversations: chatConversationsReducer,
  workspaces: workspacesReducer,
  ui: uiReducer,
  agents: agentsReducer,
  workflows: workflowsReducer,
});

// Persist configuration
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'ui'], // Only persist auth and ui state
  blacklist: ['chatConversations', 'workspaces', 'agents', 'workflows'], // Don't persist these
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export const persistor = persistStore(store);

export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
