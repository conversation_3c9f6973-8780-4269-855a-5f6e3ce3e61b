interface LLMProvider {
  id: string;
  name: string;
  models: string[];
}

interface Config {
  API_BASE_URL: string;
  MAX_FILE_SIZE: number;
  SUPPORTED_FILE_TYPES: string[];
  LLM_PROVIDERS: LLMProvider[];
  ALLOW_PREVIEW_FEATURES: boolean;
}

// Lógica para determinar a URL da API
// Padrão: mock server DESABILITADO (undefined !== 'true')
const shouldUseMockServer = import.meta.env.VITE_USE_MOCK_SERVER === 'true';

const getApiBaseUrl = () => {
  // Se estiver usando mock server, retorna string vazia para interceptação
  if (shouldUseMockServer) {
    return '';
  }
  
  // PADRÃO DE PRODUÇÃO: sempre usar URL de produção
  // Só pode ser sobrescrito explicitamente via VITE_API_BASE_URL
  return import.meta.env.VITE_API_BASE_URL || 'https://cognit-ai-s3q92.ondigitalocean.app';
};

const apiBaseUrl = getApiBaseUrl();

export const config: Config = {
  API_BASE_URL: apiBaseUrl,
  MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE || '10485760'), // PADRÃO: 10MB
  SUPPORTED_FILE_TYPES: ['pdf', 'doc', 'docx', 'txt', 'md'],
  ALLOW_PREVIEW_FEATURES: import.meta.env.VITE_ALLOW_PREVIEW_FEATURES === 'true', // PADRÃO: false (undefined !== 'true')
  LLM_PROVIDERS: [
    {
      id: 'openai',
      name: 'OpenAI',
      models: ['gpt-4-turbo', 'gpt-3.5-turbo'],
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      models: ['claude-3-opus', 'claude-3-sonnet'],
    },
    {
      id: 'google',
      name: 'Google',
      models: ['gemini-pro'],
    },
    {
      id: 'meta',
      name: 'Meta',
      models: ['llama-2-70b'],
    },
  ],
};
