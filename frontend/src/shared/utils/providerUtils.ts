/**
 * Utility functions for provider detection and styling
 */

export type ProviderType = 'openai' | 'anthropic' | 'google' | 'meta';

/**
 * Determine provider based on model name
 */
export const getProviderFromModel = (model: string): ProviderType => {
  if (!model) return 'openai';
  
  const modelLower = model.toLowerCase();
  
  if (modelLower.includes('gpt') || modelLower.includes('openai')) return 'openai';
  if (modelLower.includes('claude') || modelLower.includes('anthropic')) return 'anthropic';
  if (modelLower.includes('gemini') || modelLower.includes('google')) return 'google';
  if (modelLower.includes('llama') || modelLower.includes('meta')) return 'meta';
  
  return 'openai'; // default
};

/**
 * Get provider-specific gradient classes for styling
 * All backgrounds are orange as requested
 */
export const getProviderGradient = (_provider: ProviderType): string => {
  // Always return orange gradient regardless of provider
  return 'from-orange-500 to-red-500';
};

/**
 * Get provider-specific colors for theming
 */
export const getProviderColors = (provider: ProviderType) => {
  switch (provider) {
    case 'openai':
      return {
        primary: '#10A37F',
        secondary: '#059669',
        gradient: 'from-green-500 to-emerald-600',
        textColor: 'text-green-600',
        bgColor: 'bg-green-50'
      };
    case 'anthropic':
      return {
        primary: '#D4A574',
        secondary: '#B8956A',
        gradient: 'from-amber-500 to-orange-600',
        textColor: 'text-amber-600',
        bgColor: 'bg-amber-50'
      };
    case 'google':
      return {
        primary: '#4285F4',
        secondary: '#1976D2',
        gradient: 'from-blue-500 to-indigo-600',
        textColor: 'text-blue-600',
        bgColor: 'bg-blue-50'
      };
    case 'meta':
      return {
        primary: '#1877F2',
        secondary: '#0366D6',
        gradient: 'from-blue-600 to-purple-600',
        textColor: 'text-blue-600',
        bgColor: 'bg-blue-50'
      };
    default:
      return {
        primary: '#6B7280',
        secondary: '#4B5563',
        gradient: 'from-gray-500 to-gray-600',
        textColor: 'text-gray-600',
        bgColor: 'bg-gray-50'
      };
  }
};