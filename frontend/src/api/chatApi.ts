import { apiClient, uploadFile } from './axiosConfig';
import { logger } from '../shared/utils/logger';
import { webSocketManager } from './WebSocketManager';
import type {
  Message,
  Conversation,
  SendMessageRequest,
  StreamMessageRequest,
  CreateConversationRequest,
  UpdateConversationRequest,
  SearchConversationsRequest,
  ConversationsListResponse,
  StreamingResponse,
  MessageAttachment,
  MessageFeedback
} from '../types/chat';

// Tipos para dados do backend
interface BackendMessage {
  id?: string | number;
  content?: string;
  author?: string;
  role?: string;
  timestamp?: string;
  attached_files?: Array<{ name: string; mime_type: string }> | null;
}

interface BackendSession {
  session_id?: string | number;
  id?: string | number;
  title?: string;
  messages?: BackendMessage[];
  provider?: string;
  model?: string;
  created_at?: string;
  updated_at?: string;
  first_interaction?: string;
  last_interaction?: string;
  is_favorite?: boolean;
  tags?: string[];
  summary?: string;
  total_tokens?: number;
  totalTokens?: number;
  total_cost?: number;
  totalCost?: number;
  last_message_at?: string;
  lastMessageAt?: string;
  message_count?: number;
  messageCount?: number;
}

export interface SendFeedbackRequest {
  messageId: string;
  liked: boolean | null;
  rating?: number;
  comment?: string;
}

export interface RegenerateMessageRequest {
  messageId: string;
  temperature?: number;
  maxTokens?: number;
}

// Interfaces de providers e modelos
export interface LLMProvider {
  id: string;
  name: string;
  description: string;
  isAvailable: boolean;
  models: LLMModel[];
  supportedFeatures: string[];
}

export interface LLMModel {
  id: string;
  name: string;
  description: string;
  providerId: string;
  contextLength: number;
  inputCostPer1kTokens: number;
  outputCostPer1kTokens: number;
  isAvailable: boolean;
  supportedFeatures: ModelFeature[];
  parameters: ModelParameters;
}

export interface ModelFeature {
  name: string;
  supported: boolean;
  description?: string;
}

export interface ModelParameters {
  temperature: {
    min: number;
    max: number;
    default: number;
    step: number;
  };
  maxTokens: {
    min: number;
    max: number;
    default: number;
  };
  topP: {
    min: number;
    max: number;
    default: number;
    step: number;
  };
}

// Helper para mapear dados do backend
const mapBackendSessionToConversation = (session: BackendSession): Conversation => {
  const sessionId = session.session_id?.toString() || session.id?.toString() || '';
  
  // Gerar título mais amigável se necessário
  let title = session.title || 'Conversa sem título';
  if (title.includes('WebSocket Session') && session.messages && session.messages.length > 0) {
    const firstUserMessage = session.messages.find((msg: BackendMessage) => msg.author === 'user' || msg.role === 'user');
    if (firstUserMessage && firstUserMessage.content) {
      const content = firstUserMessage.content.trim();
      title = content.length > 50 ? content.substring(0, 50) + '...' : content;
    } else {
      title = 'Nova conversa';
    }
  }
  
  return {
    id: sessionId,
    sessionId: sessionId,
    title: title,
    messages: (session.messages || []).map((msg: BackendMessage) => {
      return {
        id: msg.id?.toString() || '',
        content: msg.content || '',
        role: (msg.author === 'user' ? 'user' : msg.author === 'bot' ? 'assistant' : (msg.role as 'user' | 'assistant' | 'system' || 'assistant')),
        timestamp: msg.timestamp || new Date().toISOString(),
        conversationId: sessionId,
        files: msg.attached_files || undefined
      };
    }),
    provider: session.provider || 'openai',
    model: session.model || 'gpt-4o',
    createdAt: session.created_at || session.first_interaction || new Date().toISOString(),
    updatedAt: session.updated_at || session.last_interaction || new Date().toISOString(),
    isFavorite: session.is_favorite || false,
    tags: session.tags || [],
    summary: session.summary || '',
    totalTokens: session.total_tokens || session.totalTokens || 0,
    totalCost: session.total_cost || session.totalCost || 0,
    lastMessageAt: session.last_message_at || session.lastMessageAt || session.last_interaction || new Date().toISOString(),
    messageCount: session.message_count || session.messageCount || (session.messages?.length || 0)
  };
};

// API methods
export const chatApi = {
  // Enviar mensagem (normal) - Integração com backend real
  sendMessage: async (request: SendMessageRequest): Promise<Message> => {
    try {
      console.log('Enviando mensagem para backend', { 
        model: request.model, 
        contentLength: request.content.length 
      });

      // Criar FormData para envio
      const formData = new FormData();
      formData.append('content', request.content);
      formData.append('provider', request.provider);
      formData.append('model', request.model);
      formData.append('stream', request.stream ? 'true' : 'false');
      
      if (request.sessionId) {
        formData.append('sessionId', request.sessionId);
      }
      
      if (request.files) {
        request.files.forEach((file, index) => {
          formData.append(`file_${index}`, file);
        });
      }

      const response = await apiClient.post('/chat/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      logger.error('Erro ao enviar mensagem', error);
      throw error;
    }
  },

  // Enviar mensagem com streaming - WebSocket via WebSocketManager
  sendStreamMessage: (
    request: StreamMessageRequest,
    onMessage: (data: StreamingResponse) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void,
    onSessionCreated?: (sessionId: string) => void,
    fileId?: string
  ): (() => void) => {
    return webSocketManager.sendMessage(
      request.sessionId,
      request.content,
      request.model,
      {
        onMessage,
        onError,
        onComplete,
        onSessionCreated
      },
      fileId
    );
  },

  // Regenerar mensagem
  regenerateMessage: async (request: RegenerateMessageRequest): Promise<Message> => {
    const response = await apiClient.post(`/chat/messages/${request.messageId}/regenerate`, {
      temperature: request.temperature,
      maxTokens: request.maxTokens,
    });
    return response.data;
  },

  // Gestão de conversas
  createConversation: async (request: CreateConversationRequest): Promise<Conversation> => {
    const response = await apiClient.post('/chat/conversations', request);
    return response.data;
  },

  getConversations: async (
    limit: number = 50,
    offset: number = 0,
    search?: string
  ): Promise<ConversationsListResponse> => {
    try {
      // Convertendo para paginação baseada em página (backend usa page, não offset)
      const page = Math.floor(offset / limit) + 1;
      
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: limit.toString(),
      });

      if (search) {
        params.append('search', search);
      }

      const response = await apiClient.get(`/api/v1/log-chat/sessions?${params.toString()}`);
      
      // Adaptando resposta do backend para o formato esperado pelo frontend
      const data = response.data;
      
      // Tentativas diferentes de mapeamento baseado na estrutura do backend
      let rawSessions = [];
      if (Array.isArray(data)) {
        rawSessions = data;
      } else if (data.items) {
        rawSessions = data.items;
      } else if (data.sessions) {
        rawSessions = data.sessions;
      } else if (data.data) {
        rawSessions = data.data;
      }
      
      // Mapear sessões do backend para formato frontend
      const conversations = rawSessions
        .filter((session: BackendSession) => {
          // Incluir apenas sessões com dados válidos
          return session.session_id || session.id;
        })
        .map((session: BackendSession) => {
          return mapBackendSessionToConversation(session);
        });
      
      return {
        conversations: conversations,
        total: data.total || data.count || conversations.length,
        hasMore: data.has_next || data.hasMore || false
      };
      
    } catch {
      // Se der erro, retornar estrutura vazia
      return {
        conversations: [],
        total: 0,
        hasMore: false
      };
    }
  },

  getConversation: async (id: string): Promise<Conversation> => {
    try {
      const response = await apiClient.get(`/api/v1/log-chat/sessions/${id}`);
      const session = response.data;
      
      return mapBackendSessionToConversation(session);
    } catch (error) {
      logger.error('Erro ao buscar conversa', error);
      throw error;
    }
  },

  updateConversation: async (
    id: string,
    request: UpdateConversationRequest
  ): Promise<Conversation> => {
    try {
      // Se está atualizando apenas o título, usar endpoint específico
      if (request.title && Object.keys(request).length === 1) {
        const response = await apiClient.patch(`/api/v1/log-chat/sessions/${id}/title`, {
          title: request.title
        });

        return mapBackendSessionToConversation(response.data);
      }
      
      // Para outras atualizações, usar endpoint geral
      const response = await apiClient.put(`/api/v1/log-chat/sessions/${id}`, request);
      return response.data;
    } catch (error) {
      logger.error('Erro ao atualizar conversa', error);
      throw error;
    }
  },

  deleteConversation: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/api/v1/log-chat/sessions/${id}`);
    } catch (error) {
      logger.error('Erro ao deletar conversa', error);
      throw error;
    }
  },

  searchConversations: async (request: SearchConversationsRequest): Promise<ConversationsListResponse> => {
    try {
      const response = await apiClient.post('/api/v1/log-chat/sessions/search', request);
      
      // Adaptando resposta do backend para o formato esperado pelo frontend
      const data = response.data;
      return {
        conversations: data.items || data.sessions || [],
        total: data.total || 0,
        hasMore: data.has_next || false
      };
    } catch (error) {
      logger.error('Erro ao buscar conversas', error);
      throw error;
    }
  },

  // Favoritos
  toggleFavorite: async (conversationId: string): Promise<{ isFavorite: boolean }> => {
    const response = await apiClient.post(`/chat/conversations/${conversationId}/favorite`);
    return response.data;
  },

  getFavoriteConversations: async (): Promise<Conversation[]> => {
    const response = await apiClient.get('/chat/conversations/favorites');
    return response.data;
  },

  // Mensagens
  getMessage: async (messageId: string): Promise<Message> => {
    const response = await apiClient.get(`/chat/messages/${messageId}`);
    return response.data;
  },

  deleteMessage: async (messageId: string): Promise<void> => {
    await apiClient.delete(`/chat/messages/${messageId}`);
  },

  // Feedback de mensagens
  sendFeedback: async (request: SendFeedbackRequest): Promise<MessageFeedback> => {
    const response = await apiClient.post(`/chat/messages/${request.messageId}/feedback`, {
      liked: request.liked,
      rating: request.rating,
      comment: request.comment,
    });
    return response.data;
  },

  // Upload de arquivos
  uploadFile: async (
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<MessageAttachment> => {
    const response = await uploadFile('/chat/upload', file, onProgress);
    return response.data;
  },

  uploadMultipleFiles: async (
    files: File[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<MessageAttachment[]> => {
    const uploads = files.map(async (file, index) => {
      return uploadFile('/chat/upload', file, (progress) => {
        onProgress?.(index, progress);
      });
    });

    const responses = await Promise.all(uploads);
    return responses.map(response => response.data);
  },

  // Upload de arquivo para chat (retorna file_id)
  uploadFileForChat: async (
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<{ file_id: string; filename: string; file_type: string; file_size: number; upload_timestamp: string; expiry_timestamp: string }> => {
    try {
      console.log('Uploading file for chat', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      // Criar FormData para envio
      const formData = new FormData();
      formData.append('file', file);

      console.log('FormData criado com 1 arquivo', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      // Debug: verificar conteúdo do FormData
      console.log('FormData entries:', {
        entries: Array.from(formData.entries()).map(([key, value]) => ({
          key,
          value: value instanceof File ? `File: ${value.name}` : value
        }))
      });

      const response = await apiClient.post('/api/v1/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(progress);
          }
        },
      });

      console.log('📤 Upload response received', {
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        hasFileId: !!(response.data && response.data.file_id),
        fileId: response.data?.file_id
      });

      // Verificar se a resposta tem a estrutura esperada
      if (!response.data || !response.data.file_id) {
        throw new Error('Resposta do upload não contém file_id');
      }

      console.log('✅ File uploaded successfully', {
        fileId: response.data.file_id,
        filename: response.data.filename
      });

      return response.data;
    } catch (error) {
      logger.error('Erro ao fazer upload dos arquivos', error);
      throw error;
    }
  },

  deleteFile: async (fileId: string): Promise<void> => {
    await apiClient.delete(`/chat/files/${fileId}`);
  },

  // Providers e modelos - Integração com backend real
  getProviders: async (): Promise<LLMProvider[]> => {
    const response = await apiClient.get('/chat/providers');
    return response.data;
  },

  getModels: async (provider?: string): Promise<import('../types').LLMModel[]> => {
    try {
      const response = await apiClient.get('/api/v1/llm-models/');
      const backendResponse = response.data;

      // O backend retorna { models: [...], total: number }
      const backendModels = backendResponse.models || backendResponse;

      // Converter modelos do backend para formato do frontend
      let filteredModels = backendModels;

      // Filtrar por provider se especificado
      if (provider) {
        filteredModels = backendModels.filter((model: BackendModel) => model.provider === provider);
      }

      return filteredModels
        .map((model: BackendModel) => ({
          id: model.id.toString(),
          name: model.name,
          provider: model.provider,
          description: `Modelo ${model.name} do provedor ${model.provider}`,
          contextWindow: model.context_window || 4096,
          pricing: {
            input: Number(model.price_usd) || 0,
            output: Number(model.price_usd) || 0,
          },
          capabilities: ['chat', 'streaming'],
          color: model.provider === 'openai' ? 'from-green-500 to-emerald-600' :
                 model.provider === 'anthropic' ? 'from-purple-500 to-indigo-600' :
                 model.provider === 'google' ? 'from-yellow-500 to-orange-600' : 'from-gray-500 to-gray-600',
          priceCategory: Number(model.price_usd) > 0.01 ? 'high' :
                        Number(model.price_usd) > 0.005 ? 'medium' : 'low'
        }));
    } catch (error) {
      logger.error('Erro ao carregar modelos LLM', error);
      return [];
    }
  },

  getModel: async (_providerId: string, modelId: string): Promise<import('../types').LLMModel> => {
    const response = await apiClient.get(`/api/v1/llm-models/${modelId}`);
    const model = response.data;

    return {
      id: model.id.toString(),
      name: model.name,
      provider: model.provider,
      description: `Modelo ${model.name} do provedor ${model.provider}`,
      contextWindow: model.context_window || 4096,
      pricing: {
        input: Number(model.price_usd) || 0,
        output: Number(model.price_usd) || 0,
      },
      capabilities: ['chat', 'streaming'],
      color: model.provider === 'openai' ? 'from-green-500 to-emerald-600' :
             model.provider === 'anthropic' ? 'from-purple-500 to-indigo-600' :
             model.provider === 'google' ? 'from-yellow-500 to-orange-600' : 'from-gray-500 to-gray-600',
      priceCategory: Number(model.price_usd) > 0.01 ? 'high' :
                    Number(model.price_usd) > 0.005 ? 'medium' : 'low'
    };
  },

  // Estatísticas
  getUsageStats: async (
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<UsageStats> => {
    const response = await apiClient.get(`/chat/stats/usage?period=${period}`);
    return response.data;
  },

  getCostStats: async (
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<CostStats> => {
    const response = await apiClient.get(`/chat/stats/costs?period=${period}`);
    return response.data;
  },

  // Exportar dados
  exportConversation: async (
    conversationId: string,
    format: 'json' | 'csv' | 'pdf' = 'json'
  ): Promise<Blob> => {
    const response = await apiClient.get(
      `/chat/conversations/${conversationId}/export?format=${format}`,
      { responseType: 'blob' }
    );
    return response.data;
  },

  exportAllConversations: async (
    format: 'json' | 'csv' = 'json'
  ): Promise<Blob> => {
    const response = await apiClient.get(
      `/chat/conversations/export?format=${format}`,
      { responseType: 'blob' }
    );
    return response.data;
  },
};

// Interface para modelo do backend
interface BackendModel {
  id: number;
  name: string;
  provider: string;
  is_active: boolean;
  context_window?: number;
  max_tokens?: number;
  price_usd?: string | number; // Pode vir como string do backend
}

// Interfaces para estatísticas
export interface UsageStats {
  totalMessages: number;
  totalTokens: number;
  totalConversations: number;
  byProvider: Record<string, {
    messages: number;
    tokens: number;
    conversations: number;
  }>;
  byModel: Record<string, {
    messages: number;
    tokens: number;
    conversations: number;
  }>;
  timeline: Array<{
    date: string;
    messages: number;
    tokens: number;
  }>;
}

export interface CostStats {
  totalCost: number;
  currency: string;
  byProvider: Record<string, number>;
  byModel: Record<string, number>;
  timeline: Array<{
    date: string;
    cost: number;
  }>;
}

// Utilitários
export const chatUtils = {
  // Calcular custo estimado de uma mensagem
  calculateCost: (tokens: { prompt: number; completion: number }, model: LLMModel): number => {
    const promptCost = (tokens.prompt / 1000) * model.inputCostPer1kTokens;
    const completionCost = (tokens.completion / 1000) * model.outputCostPer1kTokens;
    return promptCost + completionCost;
  },

  // Formatar custo para exibição
  formatCost: (cost: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency,
      minimumFractionDigits: 4,
    }).format(cost);
  },

  // Gerar título automático para conversa
  generateConversationTitle: (firstMessage: string): string => {
    const maxLength = 50;
    const cleaned = firstMessage.trim().replace(/\n+/g, ' ');
    
    if (cleaned.length <= maxLength) {
      return cleaned;
    }
    
    return cleaned.substring(0, maxLength - 3) + '...';
  },

  // Estimar tokens (aproximação simples)
  estimateTokens: (text: string): number => {
    // Aproximação: 1 token ≈ 4 caracteres
    return Math.ceil(text.length / 4);
  },
};

export default chatApi;
