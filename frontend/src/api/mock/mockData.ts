// Mock data for all entities

export const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=ricardo',
    role: 'admin' as const,
    isEmailVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    preferences: {
      theme: 'light' as const,
      language: 'pt' as const,
      notifications: {
        email: true,
        push: true,
        marketing: false,
      },
    },
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=ana',
    role: 'user' as const,
    isEmailVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    preferences: {
      theme: 'light' as const,
      language: 'pt' as const,
      notifications: {
        email: true,
        push: true,
        marketing: false,
      },
    },
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=carlos',
    role: 'user' as const,
    isEmailVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    preferences: {
      theme: 'light' as const,
      language: 'pt' as const,
      notifications: {
        email: true,
        push: true,
        marketing: false,
      },
    },
  },
  {
    id: '4',
    name: 'João Silva',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=joao',
    role: 'admin' as const,
    isEmailVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    preferences: {
      theme: 'light' as const,
      language: 'pt' as const,
      notifications: {
        email: true,
        push: true,
        marketing: false,
      },
    },
  },
];

export const mockAgents = [
  {
    id: 'presentation-expert',
    name: 'Presentation Expert',
    description:
      'Creates professional PowerPoint presentations from your documents and data',
    icon: 'presentation',
    color: 'from-orange-500 to-red-500',
    capabilities: [
      'PowerPoint Creation',
      'Slide Design',
      'Data Visualization',
      'Content Structuring',
    ],
    examples: [
      'Create a presentation about our Q4 results',
      'Build slides summarizing the AI research findings',
      'Make a pitch deck for the new product launch',
    ],
    category: 'presentation' as const,
    isActive: true,
    usage: {
      totalRequests: 15,
      successRate: 93.3,
      avgResponseTime: 45000,
    },
  },
  {
    id: 'document-analyst',
    name: 'Document Analyst',
    description:
      'Analyzes documents, extracts insights, and provides comprehensive summaries',
    icon: 'file-text',
    color: 'from-blue-500 to-indigo-500',
    capabilities: [
      'Document Analysis',
      'Content Extraction',
      'Insight Generation',
      'Summarization',
    ],
    examples: [
      'Analyze the contract terms and highlight key points',
      'Summarize all research papers in the ML workspace',
      'Extract action items from meeting notes',
    ],
    category: 'analysis' as const,
    isActive: true,
    usage: {
      totalRequests: 12,
      successRate: 100,
      avgResponseTime: 32000,
    },
  },
  {
    id: 'data-scientist',
    name: 'Data Scientist',
    description:
      'Processes data, creates visualizations, and generates analytical reports',
    icon: 'bar-chart-3',
    color: 'from-green-500 to-emerald-500',
    capabilities: [
      'Data Analysis',
      'Statistical Modeling',
      'Visualization',
      'Report Generation',
    ],
    examples: [
      'Create charts from the sales data spreadsheet',
      'Analyze user behavior patterns',
      'Generate a data quality report',
    ],
    category: 'data' as const,
    isActive: true,
    usage: {
      totalRequests: 8,
      successRate: 87.5,
      avgResponseTime: 67000,
    },
  },
  {
    id: 'code-assistant',
    name: 'Code Assistant',
    description:
      'Reviews code, generates documentation, and creates technical specifications',
    icon: 'code',
    color: 'from-purple-500 to-pink-500',
    capabilities: [
      'Code Review',
      'Documentation',
      'API Specs',
      'Technical Writing',
    ],
    examples: [
      'Generate API documentation from the codebase',
      'Review the authentication module',
      'Create technical specifications for the new feature',
    ],
    category: 'code' as const,
    isActive: true,
    usage: {
      totalRequests: 6,
      successRate: 83.3,
      avgResponseTime: 52000,
    },
  },
  {
    id: 'content-writer',
    name: 'Content Writer',
    description: 'Creates engaging content, reports, and marketing materials',
    icon: 'pen-tool',
    color: 'from-teal-500 to-cyan-500',
    capabilities: [
      'Content Creation',
      'Copywriting',
      'Report Writing',
      'Marketing Materials',
    ],
    examples: [
      'Write a blog post about our latest features',
      'Create marketing copy for the product launch',
      'Draft a comprehensive project report',
    ],
    category: 'content' as const,
    isActive: true,
    usage: {
      totalRequests: 4,
      successRate: 100,
      avgResponseTime: 38000,
    },
  },
];

export const mockAgentTasks = [
  {
    id: 'task-1',
    agentId: 'presentation-expert',
    workspaceId: 'ws-1',
    title: 'Criar apresentação Q4 2024',
    description:
      'Criar uma apresentação profissional sobre os resultados do Q4 2024 baseada nos relatórios financeiros',
    status: 'completed' as const,
    priority: 'high' as const,
    input: {
      prompt:
        'Criar uma apresentação profissional sobre os resultados do Q4 2024 baseada nos relatórios financeiros',
      documentIds: ['doc-1', 'doc-2', 'doc-3'],
      parameters: {},
    },
    output: {
      files: [
        {
          id: 'file-1',
          name: 'Q4_2024_Results.pptx',
          type: 'presentation' as const,
          format: 'pptx',
          size: 3145728,
          downloadUrl: '#download',
          previewUrl: '#preview',
          description: 'Apresentação de 18 slides com resultados Q4 2024',
        },
      ],
      summary:
        'Apresentação criada com 18 slides cobrindo receita, despesas, crescimento e projeções para 2025.',
      insights: [
        'Crescimento de 23% na receita comparado ao Q3',
        'Redução de 15% nos custos operacionais',
        'Meta de receita para 2025 estabelecida',
      ],
    },
    progress: 100,
    createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    updatedAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    completedAt: new Date(Date.now() - 3600000).toISOString(),
  },
  {
    id: 'task-2',
    agentId: 'document-analyst',
    workspaceId: 'ws-1',
    title: 'Análise de contratos fornecedores',
    description:
      'Analisar 12 contratos de fornecedores e identificar pontos-chave e riscos',
    status: 'processing' as const,
    priority: 'medium' as const,
    input: {
      prompt:
        'Analisar 12 contratos de fornecedores e identificar pontos-chave e riscos',
      documentIds: Array.from({ length: 12 }, (_, i) => `contract-${i + 1}`),
      parameters: {},
    },
    progress: 67,
    createdAt: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    updatedAt: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
  },
  {
    id: 'task-3',
    agentId: 'data-scientist',
    workspaceId: 'ws-1',
    title: 'Dashboard de métricas de vendas',
    description:
      'Criar dashboard interativo com métricas de vendas dos últimos 6 meses',
    status: 'pending' as const,
    priority: 'low' as const,
    input: {
      prompt:
        'Criar dashboard interativo com métricas de vendas dos últimos 6 meses',
      documentIds: ['sales-data.xlsx'],
      parameters: { timeRange: '6months', includeForecasting: true },
    },
    progress: 0,
    createdAt: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
    updatedAt: new Date(Date.now() - 1800000).toISOString(),
  },
];

export const mockAgentConversations = [
  {
    id: 'conv-1',
    agentId: 'presentation-expert',
    workspaceId: 'ws-1',
    title: 'Apresentação AI Research',
    messages: [
      {
        id: 'msg-1',
        type: 'user' as const,
        content: 'Preciso de uma apresentação sobre nossas pesquisas em IA',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        attachments: ['research-paper-1.pdf', 'research-paper-2.pdf'],
      },
      {
        id: 'msg-2',
        type: 'agent' as const,
        content:
          'Perfeito! Analisei os papers de pesquisa e vou criar uma apresentação abrangente. Incluirei os principais insights, metodologias e resultados.',
        timestamp: new Date(Date.now() - 3580000).toISOString(),
        agentId: 'presentation-expert',
        actions: [
          {
            id: 'action-1',
            type: 'file_creation' as const,
            title: 'AI_Research_Presentation.pptx',
            description:
              '15 slides cobrindo metodologias, resultados e insights',
            status: 'completed' as const,
            result: {
              id: 'file-pres-1',
              name: 'AI_Research_Presentation.pptx',
              type: 'presentation' as const,
              format: 'pptx',
              size: 2621440,
              downloadUrl: '#download',
              previewUrl: '#preview',
              description: '15 slides sobre pesquisa em IA',
            },
            progress: 100,
            startedAt: new Date(Date.now() - 3580000).toISOString(),
            completedAt: new Date(Date.now() - 3300000).toISOString(),
          },
        ],
      },
    ],
    status: 'completed' as const,
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    updatedAt: new Date(Date.now() - 3300000).toISOString(),
  },
];

// Mock Conversations - APENAS para desenvolvimento com VITE_USE_MOCK_SERVER=true
export const mockConversations = [
  {
    id: '1',
    sessionId: 'session-1',
    title: 'Análise de Performance React',
    messages: [
      {
        id: '1',
        content: 'Preciso de ajuda para otimizar a performance da minha aplicação React. Ela está carregando lentamente.',
        role: 'user' as const,
        timestamp: '2024-01-08T14:30:00Z',
        conversationId: '1',
      },
      {
        id: '2',
        content:
          'Vou te ajudar a otimizar sua aplicação React! Primeiro, vamos identificar os gargalos:\n\n**Análise inicial:**\n- Bundle size muito grande?\n- Re-renderizações desnecessárias?\n- Componentes não otimizados?\n\n**Principais estratégias:**\n\n1. **Code Splitting**\n```javascript\nconst LazyComponent = React.lazy(() => import("./Component"));\n```\n\n2. **Memoização**\n```javascript\nconst MemoComponent = React.memo(Component);\nconst memoizedValue = useMemo(() => calculation, [deps]);\n```\n\n3. **Bundle Analysis**\n```bash\nnpm install --save-dev webpack-bundle-analyzer\n```\n\nQual é o principal problema que você está enfrentando?',
        role: 'assistant' as const,
        timestamp: '2024-01-08T14:31:45Z',
        conversationId: '1',
        provider: 'anthropic',
        model: 'claude-3-sonnet',
        tokens: { prompt: 120, completion: 350, total: 470 },
        cost: 0.0089,
      },
      {
        id: '3',
        content: 'O bundle está em 2.5MB e tenho muitos re-renders. Como posso resolver isso?',
        role: 'user' as const,
        timestamp: '2024-01-08T14:35:00Z',
        conversationId: '1',
      },
      {
        id: '4',
        content:
          '2.5MB é realmente muito! Vamos atacar os dois problemas:\n\n**Para reduzir o bundle:**\n\n1. **Tree shaking** - remova imports não utilizados\n2. **Dynamic imports** para rotas:\n```javascript\nconst HomePage = lazy(() => import("./pages/Home"));\nconst DashboardPage = lazy(() => import("./pages/Dashboard"));\n```\n\n3. **Análise de dependências**:\n```bash\nnpx webpack-bundle-analyzer build/static/js/*.js\n```\n\n**Para eliminar re-renders:**\n\n1. **useCallback** para funções:\n```javascript\nconst handleClick = useCallback(() => {\n  // sua lógica\n}, [dependency]);\n```\n\n2. **React.memo** nos componentes:\n```javascript\nexport default React.memo(MyComponent);\n```\n\n3. **Profiler** para identificar componentes problemáticos\n\nImplemente essas mudanças e me conte os resultados!',
        role: 'assistant' as const,
        timestamp: '2024-01-08T14:36:30Z',
        conversationId: '1',
        provider: 'anthropic',
        model: 'claude-3-sonnet',
        tokens: { prompt: 95, completion: 280, total: 375 },
        cost: 0.0071,
      },
    ],
    provider: 'anthropic',
    model: 'claude-3-sonnet',
    createdAt: '2024-01-08T14:30:00Z',
    updatedAt: '2024-01-08T14:36:30Z',
    isFavorite: true,
    tags: ['react', 'performance', 'otimização'],
    totalTokens: 845,
    totalCost: 0.016,
    lastMessageAt: '2024-01-08T14:36:30Z',
    messageCount: 4,
  },
  {
    id: '2',
    sessionId: 'session-2',
    title: 'Implementação de WebSockets',
    messages: [
      {
        id: '3',
        content: 'Como implementar WebSockets para chat em tempo real com Node.js e React?',
        role: 'user' as const,
        timestamp: '2024-01-08T16:15:00Z',
        conversationId: '2',
      },
      {
        id: '4',
        content:
          'Excelente pergunta! Vou te mostrar uma implementação completa de WebSockets para chat:\n\n**Backend (Node.js + Socket.io):**\n\n```javascript\nconst express = require("express");\nconst http = require("http");\nconst socketIo = require("socket.io");\n\nconst app = express();\nconst server = http.createServer(app);\nconst io = socketIo(server, {\n  cors: {\n    origin: "http://localhost:3000",\n    methods: ["GET", "POST"]\n  }\n});\n\nio.on("connection", (socket) => {\n  console.log("Usuário conectado:", socket.id);\n  \n  socket.on("join-room", (roomId) => {\n    socket.join(roomId);\n  });\n  \n  socket.on("send-message", (data) => {\n    io.to(data.roomId).emit("receive-message", data);\n  });\n  \n  socket.on("disconnect", () => {\n    console.log("Usuário desconectado:", socket.id);\n  });\n});\n\nserver.listen(3001);\n```\n\n**Frontend (React):**\n\n```javascript\nimport { useEffect, useState } from "react";\nimport io from "socket.io-client";\n\nconst Chat = () => {\n  const [socket, setSocket] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [message, setMessage] = useState("");\n  \n  useEffect(() => {\n    const newSocket = io("http://localhost:3001");\n    setSocket(newSocket);\n    \n    newSocket.on("receive-message", (data) => {\n      setMessages(prev => [...prev, data]);\n    });\n    \n    return () => newSocket.close();\n  }, []);\n  \n  const sendMessage = () => {\n    if (socket && message) {\n      socket.emit("send-message", {\n        roomId: "general",\n        message,\n        timestamp: new Date()\n      });\n      setMessage("");\n    }\n  };\n  \n  return (\n    <div>\n      <div>\n        {messages.map((msg, index) => (\n          <div key={index}>{msg.message}</div>\n        ))}\n      </div>\n      <input \n        value={message}\n        onChange={(e) => setMessage(e.target.value)}\n        onKeyPress={(e) => e.key === "Enter" && sendMessage()}\n      />\n      <button onClick={sendMessage}>Enviar</button>\n    </div>\n  );\n};\n```\n\nIsso cria uma base sólida para chat em tempo real!',
        role: 'assistant' as const,
        timestamp: '2024-01-08T16:17:30Z',
        conversationId: '2',
        provider: 'openai',
        model: 'gpt-4-turbo',
        tokens: { prompt: 85, completion: 520, total: 605 },
        cost: 0.0201,
      },
    ],
    provider: 'openai',
    model: 'gpt-4-turbo',
    createdAt: '2024-01-08T16:15:00Z',
    updatedAt: '2024-01-08T16:17:30Z',
    isFavorite: false,
    tags: ['websockets', 'chat', 'nodejs', 'react'],
    totalTokens: 605,
    totalCost: 0.0201,
    lastMessageAt: '2024-01-08T16:17:30Z',
    messageCount: 2,
  },
  {
    id: '3',
    sessionId: 'session-3',
    title: 'Análise de dados com Python',
    messages: [
      {
        id: '5',
        content: 'Como fazer análise exploratória de dados com pandas e matplotlib?',
        role: 'user' as const,
        timestamp: '2024-01-03T09:15:00Z',
        conversationId: '3',
      },
      {
        id: '6',
        content:
          'Vou te mostrar um exemplo prático de análise exploratória de dados:\n\n```python\nimport pandas as pd\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Carregar dados\ndf = pd.read_csv("dados.csv")\n\n# Informações básicas\nprint(df.info())\nprint(df.describe())\n\n# Verificar valores nulos\nprint(df.isnull().sum())\n\n# Distribuição das variáveis\ndf.hist(figsize=(12, 8))\nplt.show()\n\n# Correlação entre variáveis\ncorr_matrix = df.corr()\nsns.heatmap(corr_matrix, annot=True)\nplt.show()\n```\n\nIsso te dará uma visão geral dos seus dados!',
        role: 'assistant' as const,
        timestamp: '2024-01-03T09:16:30Z',
        conversationId: '3',
        provider: 'anthropic',
        model: 'claude-3-sonnet',
        tokens: { prompt: 120, completion: 250, total: 370 },
        cost: 0.0045,
      },
    ],
    provider: 'anthropic',
    model: 'claude-3-sonnet',
    createdAt: '2024-01-03T09:15:00Z',
    updatedAt: '2024-01-03T09:16:30Z',
    isFavorite: false,
    tags: ['python', 'dados', 'análise'],
    totalTokens: 370,
    totalCost: 0.0045,
    lastMessageAt: '2024-01-03T09:16:30Z',
    messageCount: 2,
  }
];

export const mockWorkspaces = [
  {
    id: '1',
    name: 'Meu Workspace Principal',
    description: 'Workspace para desenvolvimento de projetos React',
    ownerId: '1',
    settings: {
      isPublic: false,
      allowInvites: true,
      defaultRole: 'viewer' as const,
      documentRetentionDays: 365,
      allowedDomains: ['company.com'],
      features: {
        aiAnalysis: true,
        documentVersioning: true,
        advancedSearch: true,
        bulkOperations: true,
        apiAccess: true,
        ssoIntegration: false,
      },
    },
    stats: {
      totalDocuments: 25,
      totalSize: 52428800, // 50MB
      totalMembers: 3,
      totalProjects: 5,
      documentsThisMonth: 8,
      storageUsed: 52428800,
      storageLimit: 1073741824, // 1GB
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isArchived: false,
    memberCount: 3,
    projectCount: 5,
    documentCount: 25,
  },
];

export const mockProjects = [
  {
    id: '1',
    name: 'Sistema de Autenticação',
    description: 'Documentação e implementação do sistema de autenticação',
    workspaceId: '1',
    status: 'active' as const,
    tags: ['autenticação', 'segurança'],
    createdBy: '1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    documentCount: 12,
    collaborators: [
      {
        userId: '1',
        role: 'owner' as const,
        addedAt: '2024-01-01T00:00:00Z',
        user: {
          id: '1',
          name: 'João Silva',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=joao',
        },
      },
    ],
    settings: {
      isPublic: false,
      allowComments: true,
      autoArchive: false,
      notificationSettings: {
        onNewDocument: true,
        onDocumentUpdate: true,
        onComments: true,
      },
    },
  },
];

// Mock LLM Providers - APENAS para desenvolvimento com VITE_USE_MOCK_SERVER=true
export const mockLLMProviders = [
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Advanced language models from OpenAI',
    isAvailable: true,
    models: [
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: 'Most capable GPT-4 model with enhanced reasoning',
        providerId: 'openai',
        provider: 'openai',
        contextLength: 128000,
        inputCostPer1kTokens: 0.01,
        outputCostPer1kTokens: 0.03,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 1, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Analysis', 'Vision'],
        color: 'from-green-500 to-emerald-600',
        pricing: { input: 0.01, output: 0.03 },
        contextWindow: 128000,
        priceCategory: 'high' as const,
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'Powerful model for complex reasoning tasks',
        providerId: 'openai',
        provider: 'openai',
        contextLength: 128000,
        inputCostPer1kTokens: 0.03,
        outputCostPer1kTokens: 0.06,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 1, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Analysis', 'Vision'],
        color: 'from-green-500 to-emerald-600',
        pricing: { input: 0.03, output: 0.06 },
        contextWindow: 128000,
        priceCategory: 'high' as const,
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        description: 'Fast and efficient model for everyday tasks',
        providerId: 'openai',
        provider: 'openai',
        contextLength: 128000,
        inputCostPer1kTokens: 0.00015,
        outputCostPer1kTokens: 0.0006,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 1, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Vision'],
        color: 'from-green-500 to-emerald-600',
        pricing: { input: 0.00015, output: 0.0006 },
        contextWindow: 128000,
        priceCategory: 'low' as const,
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and reliable model for general conversations',
        providerId: 'openai',
        provider: 'openai',
        contextLength: 16000,
        inputCostPer1kTokens: 0.0005,
        outputCostPer1kTokens: 0.0015,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 1, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code'],
        color: 'from-green-500 to-emerald-600',
        pricing: { input: 0.0005, output: 0.0015 },
        contextWindow: 16000,
        priceCategory: 'low' as const,
      },
    ],
    supportedFeatures: ['text', 'vision', 'function_calling'],
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'Claude family of models with strong reasoning',
    isAvailable: true,
    models: [
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        description: 'Most capable Claude model for complex tasks',
        providerId: 'anthropic',
        provider: 'anthropic',
        contextLength: 200000,
        inputCostPer1kTokens: 0.015,
        outputCostPer1kTokens: 0.075,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 1, default: 0.7, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Analysis', 'Complex Reasoning'],
        color: 'from-purple-500 to-indigo-600',
        pricing: { input: 0.015, output: 0.075 },
        contextWindow: 200000,
        priceCategory: 'high' as const,
      },
      {
        id: 'claude-3-sonnet',
        name: 'Claude 3 Sonnet',
        description: 'Balanced model with great performance and cost efficiency',
        providerId: 'anthropic',
        provider: 'anthropic',
        contextLength: 200000,
        inputCostPer1kTokens: 0.003,
        outputCostPer1kTokens: 0.015,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 1, default: 0.7, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Analysis'],
        color: 'from-purple-500 to-indigo-600',
        pricing: { input: 0.003, output: 0.015 },
        contextWindow: 200000,
        priceCategory: 'medium' as const,
      },
      {
        id: 'claude-3-haiku',
        name: 'Claude 3 Haiku',
        description: 'Fast and affordable model for everyday tasks',
        providerId: 'anthropic',
        provider: 'anthropic',
        contextLength: 200000,
        inputCostPer1kTokens: 0.00025,
        outputCostPer1kTokens: 0.00125,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 1, default: 0.7, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code'],
        color: 'from-purple-500 to-indigo-600',
        pricing: { input: 0.00025, output: 0.00125 },
        contextWindow: 200000,
        priceCategory: 'low' as const,
      },
    ],
    supportedFeatures: ['text', 'vision'],
  },
  {
    id: 'google',
    name: 'Google',
    description: 'Gemini models with multimodal capabilities',
    isAvailable: true,
    models: [
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: 'Advanced multimodal model with large context window',
        providerId: 'google',
        provider: 'google',
        contextLength: 2000000,
        inputCostPer1kTokens: 0.0035,
        outputCostPer1kTokens: 0.0105,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
          { name: 'video', supported: true },
          { name: 'audio', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 0.9, step: 0.1 },
          maxTokens: { min: 1, max: 8192, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Images', 'Video', 'Audio'],
        color: 'from-yellow-500 to-orange-600',
        pricing: { input: 0.0035, output: 0.0105 },
        contextWindow: 2000000,
        priceCategory: 'medium' as const,
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: 'Fast and efficient model for quick responses',
        providerId: 'google',
        provider: 'google',
        contextLength: 1000000,
        inputCostPer1kTokens: 0.00035,
        outputCostPer1kTokens: 0.00105,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'vision', supported: true },
          { name: 'video', supported: true },
          { name: 'audio', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 0.9, step: 0.1 },
          maxTokens: { min: 1, max: 8192, default: 1000 },
          topP: { min: 0, max: 1, default: 1, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Images', 'Multimodal'],
        color: 'from-yellow-500 to-orange-600',
        pricing: { input: 0.00035, output: 0.00105 },
        contextWindow: 1000000,
        priceCategory: 'low' as const,
      },
    ],
    supportedFeatures: ['text', 'vision', 'video', 'audio'],
  },
  {
    id: 'meta',
    name: 'Meta',
    description: 'Llama open-source models',
    isAvailable: true,
    models: [
      {
        id: 'llama-3.1-405b',
        name: 'Llama 3.1 405B',
        description: 'Most capable open-source model from Meta',
        providerId: 'meta',
        provider: 'meta',
        contextLength: 128000,
        inputCostPer1kTokens: 0.0027,
        outputCostPer1kTokens: 0.0027,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 0.6, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 0.9, step: 0.1 },
        },
        capabilities: ['Text', 'Code', 'Analysis'],
        color: 'from-red-500 to-pink-600',
        pricing: { input: 0.0027, output: 0.0027 },
        contextWindow: 128000,
        priceCategory: 'medium' as const,
      },
      {
        id: 'llama-3.1-70b',
        name: 'Llama 3.1 70B',
        description: 'High-performance open-source model',
        providerId: 'meta',
        provider: 'meta',
        contextLength: 128000,
        inputCostPer1kTokens: 0.00088,
        outputCostPer1kTokens: 0.00088,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
          { name: 'function_calling', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 0.6, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 0.9, step: 0.1 },
        },
        capabilities: ['Text', 'Code'],
        color: 'from-red-500 to-pink-600',
        pricing: { input: 0.00088, output: 0.00088 },
        contextWindow: 128000,
        priceCategory: 'low' as const,
      },
      {
        id: 'llama-3.1-8b',
        name: 'Llama 3.1 8B',
        description: 'Efficient model for general use',
        providerId: 'meta',
        provider: 'meta',
        contextLength: 128000,
        inputCostPer1kTokens: 0.00018,
        outputCostPer1kTokens: 0.00018,
        isAvailable: true,
        supportedFeatures: [
          { name: 'text', supported: true },
        ],
        parameters: {
          temperature: { min: 0, max: 2, default: 0.6, step: 0.1 },
          maxTokens: { min: 1, max: 4096, default: 1000 },
          topP: { min: 0, max: 1, default: 0.9, step: 0.1 },
        },
        capabilities: ['Text', 'Code'],
        color: 'from-red-500 to-pink-600',
        pricing: { input: 0.00018, output: 0.00018 },
        contextWindow: 128000,
        priceCategory: 'low' as const,
      },
    ],
    supportedFeatures: ['text', 'function_calling'],
  },
];

export const mockTools = [
  {
    id: 'web-search',
    name: 'Busca Web',
    description: 'Pesquisar informações atualizadas na internet',
    icon: 'Globe',
    color: 'from-green-500 to-green-600',
    category: 'Pesquisa'
  },
  {
    id: 'image-generator',
    name: 'Gerador de Imagens',
    description: 'Criar imagens usando IA generativa',
    icon: 'Image',
    color: 'from-pink-500 to-pink-600',
    category: 'Criativo'
  },
  {
    id: 'code-executor',
    name: 'Executor de Código',
    description: 'Executar e testar código em várias linguagens',
    icon: 'Code',
    color: 'from-indigo-500 to-indigo-600',
    category: 'Desenvolvimento'
  },
  {
    id: 'data-analyzer',
    name: 'Analista de Dados',
    description: 'Analisar dados e criar visualizações',
    icon: 'BarChart3',
    color: 'from-orange-500 to-orange-600',
    category: 'Dados'
  },
  {
    id: 'database-query',
    name: 'Consulta de Banco',
    description: 'Executar consultas em bancos de dados',
    icon: 'Database',
    color: 'from-teal-500 to-teal-600',
    category: 'Dados'
  },
  {
    id: 'slide-creator',
    name: 'Criador de Slides',
    description: 'Criar apresentações automáticas com tema da Cognit AI',
    icon: 'FileText',
    color: 'from-orange-500 to-red-500',
    category: 'Apresentação'
  }
];