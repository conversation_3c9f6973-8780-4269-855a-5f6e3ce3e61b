import { config } from '../shared/config';
import { logger } from '../shared/utils/logger';
import type { StreamingResponse } from '../types/chat';

interface WebSocketConnection {
  websocket: WebSocket;
  conversationId: string;
  clientId: string;
  sessionId?: string;
  isConnected: boolean;
}

interface StreamRequest {
  message: string;
  model_id: number;
  file_ids?: string[];
}

interface StreamCallbacks {
  onMessage: (data: StreamingResponse) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
  onSessionCreated?: (sessionId: string) => void; // Callback para quando session_id é recebido
}

class WebSocketManager {
  private connections = new Map<string, WebSocketConnection>();
  private activeStreams = new Map<string, StreamCallbacks>();

  private getClientId(): string {
    const clientId = localStorage.getItem('clientId');
    if (!clientId) {
      throw new Error('Client ID não encontrado. Por favor, reinicia a página!');
    }

    return clientId;
  }

  private createConnection(connectionId: string, sessionId?: string): Promise<WebSocketConnection> {
    return new Promise((resolve, reject) => {
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        reject(new Error('Token de autenticação não encontrado'));
        return;
      }

      const clientId = this.getClientId();

      // Construir URL do WebSocket com session_id opcional
      let wsUrl = `${config.API_BASE_URL.replace('http', 'ws')}/api/v1/chat/ws?token=${token}&client_id=${clientId}`;
      if (sessionId) {
        wsUrl += `&session_id=${sessionId}`;
      }

      console.log('Creating WebSocket connection', {
        connectionId,
        sessionId: sessionId || 'NEW_SESSION',
        isExistingSession: !!sessionId
      });

      const websocket = new WebSocket(wsUrl);
      const connection: WebSocketConnection = {
        websocket,
        conversationId: connectionId,
        clientId,
        sessionId,
        isConnected: false
      };

      websocket.onopen = () => {
        connection.isConnected = true;
        this.connections.set(connectionId, connection);
        console.log('WebSocket connection established', { connectionId });
        resolve(connection);
      };

      websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(connectionId, data);
        } catch (error) {
          logger.error('Erro ao processar mensagem WebSocket', error);
        }
      };

      websocket.onerror = (error) => {
        logger.error('Erro na conexão WebSocket', { connectionId, error });
        this.connections.delete(connectionId);
        const callbacks = this.activeStreams.get(connectionId);
        callbacks?.onError?.(new Error('Erro na conexão WebSocket'));
      };

      websocket.onclose = () => {
        console.log('WebSocket connection closed', { connectionId });
        connection.isConnected = false;
        this.connections.delete(connectionId);
        const callbacks = this.activeStreams.get(connectionId);
        if (callbacks) {
          callbacks.onComplete?.();
          this.activeStreams.delete(connectionId);
        }
      };

      // Timeout para conexão
      setTimeout(() => {
        if (!connection.isConnected) {
          websocket.close();
          reject(new Error('Timeout na conexão WebSocket'));
        }
      }, 10000);
    });
  }

  private handleMessage(connectionId: string, data: unknown) {
    const callbacks = this.activeStreams.get(connectionId);
    if (!callbacks) return;

    if (data.type === 'connection') {
      // Conexão estabelecida - capturar session_id se disponível
      if (data.session_id && callbacks.onSessionCreated) {
        console.log('Session ID received from backend', { sessionId: data.session_id });
        callbacks.onSessionCreated(data.session_id.toString());
      }
      return;
    }

    if (data.type === 'stream_start') {
      callbacks.onMessage({
        id: `stream_${Date.now()}`,
        type: 'start',
        metadata: { model: data.model, provider: data.provider }
      });
    } else if (data.type === 'stream_chunk') {
      callbacks.onMessage({
        id: `chunk_${Date.now()}`,
        type: 'content',
        content: data.chunk || ''
      });
    } else if (data.type === 'stream_complete') {
      callbacks.onMessage({
        id: `complete_${Date.now()}`,
        type: 'end',
        content: data.final_response || data.message,
        metadata: { model: data.model, provider: data.provider }
      });

      // Não fechar a conexão, apenas limpar o stream ativo
      this.activeStreams.delete(connectionId);
      callbacks.onComplete?.();
    } else if (data.type === 'stream_stopped') {
      console.log('Stream interrompido pelo backend', { connectionId, message: data.message });

      // Tratar como fim de stream quando parado pelo usuário
      callbacks.onMessage({
        id: `stopped_${Date.now()}`,
        type: 'end',
        content: '', // Não adicionar conteúdo extra quando parado
        metadata: { stopped: true }
      });

      // Limpar o stream ativo
      this.activeStreams.delete(connectionId);
      callbacks.onComplete?.();
    } else if (data.type === 'stream_interrupted') {
      console.log('Stream interrompido com conteúdo parcial', {
        connectionId,
        message: data.message,
        partialResponse: data.partial_response
      });

      // Tratar como fim de stream com conteúdo parcial preservado
      callbacks.onMessage({
        id: `interrupted_${Date.now()}`,
        type: 'end',
        content: data.partial_response || '', // Preservar conteúdo parcial
        metadata: { interrupted: true }
      });

      // Limpar o stream ativo
      this.activeStreams.delete(connectionId);
      callbacks.onComplete?.();
    } else if (data.type === 'error') {
      logger.error('Erro no WebSocket', data.message);
      callbacks.onError?.(new Error(data.message));
      this.activeStreams.delete(connectionId);
    }
  }

  sendMessage(
    sessionId: string | undefined,
    message: string,
    modelId: string,
    callbacks: StreamCallbacks,
    fileId?: string
  ): () => void {
    // Use sessionId as the connection identifier, or generate temp ID for new conversations
    const connectionId = sessionId || `temp_${Date.now()}`;
    
    // Verificar se já existe uma conexão ativa para esta sessão
    const connection = this.connections.get(connectionId);

    if (!connection || !connection.isConnected) {
      console.log('Creating new WebSocket connection for message', { connectionId });
      
      // Criar conexão de forma síncrona e lidar com o processo assíncrono
      this.createConnection(connectionId, sessionId).then((newConnection) => {
        // Registrar callbacks para esta sessão
        this.activeStreams.set(connectionId, callbacks);

        // Validar model ID
        const modelIdNum = parseInt(modelId);
        if (isNaN(modelIdNum) || modelIdNum <= 0) {
          callbacks.onError?.(new Error(`Model ID inválido: ${modelId}`));
          return;
        }

        // Enviar mensagem
        const wsMessage: StreamRequest = {
          message: message,
          model_id: modelIdNum,
          ...(fileId && { file_ids: [fileId] })
        };

        console.log('📤 Enviando mensagem via WebSocket (nova conexão)', {
          connectionId,
          messageLength: message.length,
          modelId: modelIdNum,
          fileId: fileId || null,
          hasFileId: !!fileId,
          fileIdsArray: fileId ? [fileId] : [],
          wsMessage: JSON.stringify(wsMessage)
        });

        newConnection.websocket.send(JSON.stringify(wsMessage));
        console.log('✅ Mensagem enviada via nova conexão WebSocket', { connectionId });
      }).catch((error) => {
        logger.error('Erro ao criar conexão WebSocket', error);
        callbacks.onError?.(error instanceof Error ? error : new Error('Erro ao criar conexão'));
      });
    } else {
      console.log('Using existing WebSocket connection', { connectionId });
      
      // Registrar callbacks para esta sessão
      this.activeStreams.set(connectionId, callbacks);

      // Validar model ID
      const modelIdNum = parseInt(modelId);
      if (isNaN(modelIdNum) || modelIdNum <= 0) {
        callbacks.onError?.(new Error(`Model ID inválido: ${modelId}`));
        return () => {};
      }

      // Enviar mensagem
      const wsMessage: StreamRequest = {
        message: message,
        model_id: modelIdNum,
        ...(fileId && { file_ids: [fileId] })
      };

      console.log('📤 Enviando mensagem via WebSocket (conexão existente)', {
        connectionId,
        messageLength: message.length,
        modelId: modelIdNum,
        fileId: fileId || null,
        hasFileId: !!fileId,
        fileIdsArray: fileId ? [fileId] : [],
        wsMessage: JSON.stringify(wsMessage)
      });

      connection.websocket.send(JSON.stringify(wsMessage));
      console.log('✅ Mensagem enviada via conexão WebSocket existente', { connectionId });
    }

    // Retornar função de cancelamento
    return () => {
      this.stopStream(connectionId);
    };
  }

  stopStream(connectionId: string): void {
    const callbacks = this.activeStreams.get(connectionId);
    const connection = this.connections.get(connectionId);

    if (callbacks && connection && connection.isConnected) {
      // Enviar mensagem de parada para o backend
      const stopMessage = {
        action: "stop_stream"
      };

      try {
        connection.websocket.send(JSON.stringify(stopMessage));
        console.log('Parando streaming manualmente', { connectionId });
      } catch (error) {
        logger.error('Erro ao enviar mensagem de parada', error);
      }

      this.activeStreams.delete(connectionId);
      console.log('Stream cancelado', { connectionId });
    }
  }

  closeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.websocket.close();
      this.connections.delete(connectionId);
      this.activeStreams.delete(connectionId);
      console.log('WebSocket connection closed manually', { connectionId });
    }
  }

  closeAllConnections(): void {
    console.log('Closing all WebSocket connections', { count: this.connections.size });
    for (const [, connection] of this.connections) {
      connection.websocket.close();
    }
    this.connections.clear();
    this.activeStreams.clear();
  }

  getConnectionStatus(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    return connection?.isConnected || false;
  }
}

// Singleton instance
export const webSocketManager = new WebSocketManager();

// Clean up on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    webSocketManager.closeAllConnections();
  });
}

export default webSocketManager;