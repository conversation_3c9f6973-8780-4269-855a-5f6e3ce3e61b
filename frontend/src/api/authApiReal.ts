import { apiClient } from './axiosConfig';
import type { User, AuthResponse } from './authApi';

// Interfaces específicas para o backend real
interface BackendUser {
  id: number;
  google_sub: string;
  email: string;
  name: string;
  picture: string;
}

interface BackendAuthResponse {
  access_token: string;
  token_type: string;
  user: BackendUser;
}

interface GoogleLoginResponse {
  authorization_url: string;
}

// API real para integração com o backend
export const authApiReal = {
  // Obter URL de login do Google
  getGoogleLoginUrl: async (): Promise<GoogleLoginResponse> => {
    const response = await apiClient.get('/api/v1/auth/google/login');
    return response.data;
  },

  // Processar callback do Google
  handleGoogleCallback: async (code: string): Promise<AuthResponse> => {
    const response = await apiClient.get(`/api/v1/auth/google/callback?code=${code}`);
    const backendResponse: BackendAuthResponse = response.data;
    
    return {
      user: mapBackendUser(backendResponse.user),
      token: backendResponse.access_token,
      refreshToken: backendResponse.access_token, // Backend não tem refresh token ainda
      expiresIn: 3600
    };
  },

  // Obter usuário atual
  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get('/api/v1/auth/me');
    return mapBackendUser(response.data.user);
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      await apiClient.post('/api/v1/auth/logout');
    } catch (error) {
      console.warn('Logout request failed, clearing local tokens anyway:', error);
    }
    
    // Sempre limpar tokens localmente
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('currentUser');
  },

  // Validar token atual
  validateToken: async (): Promise<User> => {
    const response = await apiClient.get('/api/v1/auth/me');
    return mapBackendUser(response.data.user);
  }
};

// Mapear usuário do backend para formato do frontend
function mapBackendUser(backendUser: BackendUser): User {
  console.log(backendUser);
  return {
    id: backendUser.id.toString(),
    email: backendUser.email,
    name: backendUser.name || backendUser.email.split('@')[0],
    role: 'user', // Default, backend não tem sistema de roles ainda
    avatar: backendUser.picture || `https://api.dicebear.com/7.x/avatars/svg?seed=${backendUser.email}`,
    isEmailVerified: true, // Assumir verificado via Google
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    preferences: {
      theme: 'light',
      language: 'pt',
      notifications: { 
        email: true, 
        push: true, 
        marketing: false 
      }
    }
  };
}

// Utilitários para integração
export const authRealUtils = {
  // Iniciar fluxo OAuth do Google
  initiateGoogleLogin: async (): Promise<void> => {
    try {
      const { authorization_url } = await authApiReal.getGoogleLoginUrl();
      window.location.href = authorization_url;
    } catch (error) {
      console.error('Erro ao iniciar login Google:', error);
      // Fallback: redirecionar diretamente para URL conhecida se a API falhar
      // URL de fallback deve usar a URL do frontend (não do backend)
      const redirectUri = import.meta.env.PROD
        ? 'https://cognit-ai-s3q92.ondigitalocean.app/auth/google/callback'
        : 'http://localhost:5173/auth/google/callback';
      
      const fallbackUrl = 'https://accounts.google.com/o/oauth2/v2/auth?' +
        'client_id=************-f8is4qr5q773blcb9k93sgalvi60ue34.apps.googleusercontent.com&' +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        'scope=openid email profile&' +
        'response_type=code&' +
        'access_type=offline&' +
        'prompt=consent';
      
      console.log('Usando URL de fallback para Google OAuth');
      window.location.href = fallbackUrl;
    }
  },

  // Verificar se estamos no callback do Google
  isGoogleCallback: (): boolean => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.has('code') && window.location.pathname.includes('auth');
  },

  // Extrair código do callback
  getCallbackCode: (): string | null => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('code');
  },

  // Verificar se houve erro no callback
  getCallbackError: (): string | null => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('error');
  }
};

export default authApiReal;