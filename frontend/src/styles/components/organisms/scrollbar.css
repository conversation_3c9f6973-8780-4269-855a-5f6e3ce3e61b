/* Scrollbar customizada laranja para o histórico */

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #f97316, #ea580c);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #ea580c, #dc2626);
}

/* Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #f97316 transparent;
}

/* Scrollbar para área de conversas */
.conversation-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.conversation-list-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.conversation-list-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #f97316, #ea580c);
  border-radius: 3px;
}

.conversation-list-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #ea580c, #dc2626);
}

/* Firefox para área de conversas */
.conversation-list-scroll {
  scrollbar-width: thin;
  scrollbar-color: #f97316 rgba(0, 0, 0, 0.05);
}
