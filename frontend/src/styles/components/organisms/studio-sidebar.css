/* Estilos para a sidebar do Studio */

.studio-sidebar {
  background-color: #F8F9FA;
  position: relative;
}

/* Botão flutuante de toggle do histórico */
.sidebar-toggle-button {
  position: absolute;
  top: 32px; /* Alinhado com o ícone do Studio (16px padding + 16px metade do ícone) */
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  z-index: 50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Posicionamento do botão quando a sidebar está fechada */
.sidebar-toggle-button.collapsed {
  left: 16px; /* Posicionado um pouco à direita quando fechado */
}

/* Posicionamento do botão quando a sidebar está expandida */
.sidebar-toggle-button:not(.collapsed) {
  left: calc(320px - 16px); /* 320px (w-80) - 16px para ficar na borda direita */
}

.sidebar-toggle-button:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-toggle-button:active {
  transform: translateY(-50%) scale(0.95);
}

/* Ícone dentro do botão */
.sidebar-toggle-button svg {
  width: 16px;
  height: 16px;
  color: #6B7280;
  transition: transform 0.2s ease-in-out;
}

.sidebar-toggle-button:hover svg {
  color: #374151;
}

/* Rotação do ícone baseado no estado */
.sidebar-toggle-button.collapsed svg {
  transform: rotate(180deg);
}
