{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "Bash(npm run type-check:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(npm ls:*)", "Bash(npm audit:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(npm run test:*)", "Bash(./quick-check.sh:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm install:*)", "Bash(node:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "Bash(PYTHONPATH=/home/<USER>/projects/cognit-ai/backend python3 database/seeders/demo_user_seeder.py)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(curl:*)", "Bash(alembic upgrade:*)", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d cognit_ai -c \"\\d chat_sessions\")"], "deny": []}}