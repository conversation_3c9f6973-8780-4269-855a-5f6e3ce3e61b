# IEBT • CognitAI


## Rodar o projeto localmente

### Configuração do ambiente

Este projeto utiliza o [Poetry](https://python-poetry.org/) para gerenciar as
dependências. Para instalar essas dependências, execute o comando:

```sh
poetry install
```

<PERSON><PERSON><PERSON> disso, para conseguir acessar as APIs e serviços externos, será necessário
criar um arquivo `.env` na raiz do projeto. Você pode copiar o arquivo
`.env.example` e preencher as variáveis fornecidas.

### Banco de Dados (PostgreSQL)

O projeto utiliza PostgreSQL como banco de dados. Para facilitar o desenvolvimento,
há um arquivo `docker-compose.yaml` que configura o banco automaticamente.

#### Subir o banco de dados:

```sh
docker-compose up -d
```

Isso irá:
- Criar um container PostgreSQL (`cognit-ai-db`) na porta `5432`
- Configurar o banco `cognit-ai` com usuário `cognit` e senha `cognit123`
- Usar armazenamento temporário (tmpfs) para desenvolvimento
- Criar uma rede isolada `cognit-ai-network`

#### Parar o banco de dados:

```sh
docker-compose down
```

#### Ver logs do banco:

```sh
docker-compose logs db
```

#### Acessar o container do banco:

```sh
docker exec -it cognit-ai-db psql -U cognit -d cognit-ai
```

**Configuração padrão:**
- Host: `localhost`
- Porta: `5432`
- Database: `cognit-ai`
- Usuário: `cognit`
- Senha: `cognit123`
- Container: `cognit-ai-db`

**⚠️ Nota:** O banco usa `tmpfs` para armazenamento, ou seja, os dados são perdidos quando o container é removido. Isso é ideal para desenvolvimento, mas para produção você deve configurar volumes persistentes.

Para rodar a API, execute o comando:

```sh
# Opção 1
python api.py

# Opção 2
uvicorn api:app --reload
```

Para rodar a API do Cognit AI, execute o comando:

```sh
poetry run python api.py
```

A API estará disponível em `http://localhost:8501` e a documentação automática em `http://localhost:8501/docs`.

## Gerenciar migrations no banco de dados (Alembic)

Para gerenciar as _migrations_ do banco de dados, é utilizado o Alembic.

### Criar nova _migration_

Para criar uma nova _migration_, execute o comando:

```sh
alembic revision --autogenerate -m "Descrição da migration"
```

### Aplicar _migrations_

Para aplicar as _migrations_ no banco de dados, execute o comando:

```sh
alembic upgrade head
```

### Reverter _migrations_

Para reverter as _migrations_ no banco de dados, execute o comando:

```sh
# See the history of migrations
alembic history

# Revert to a specific migration
alembic downgrade <hash>

# Revert the last migration
alembic downgrade -1
```

## Seeders

Para popular o banco de dados com dados iniciais:

```sh
poetry run python database/seeders/run_seeders.py
```

## Arquitetura

O projeto segue os princípios da **Arquitetura Hexagonal (Ports and Adapters)**, promovendo separação clara de responsabilidades e facilitando testes e manutenção.

### Estrutura de Camadas

```
src/
├── adapters/           # Camada de Adaptadores
├── application/        # Camada de Aplicação (Regras de Negócio)
├── presentation/       # Camada de Apresentação
└── shared_kernel/      # Módulos Compartilhados
```

### 🔌 **Adapters** - Comunicação com Serviços Externos

Responsável pela integração com APIs e serviços externos, isolando as dependências externas do core da aplicação.

```
src/adapters/
├── auth/                    # Autenticação externa
│   ├── interface.py         # Contrato de autenticação
│   ├── exceptions/          # Exceções específicas de auth
│   └── provider/
│       └── google.py        # Implementação Google OAuth
└── llm_chat/               # Integração com LLMs
    ├── interface.py        # Contrato de chat LLM
    └── provider/
        ├── openai.py       # Implementação OpenAI
        └── anthropic.py    # Implementação Anthropic
```

**Exemplos implementados:**
- `GoogleAuthAdapter`: OAuth2 com Google
- `LlmChatAdapterOpenAI`: Integração com OpenAI GPT
- `LlmChatAdapterAnthropic`: Integração com Claude

### 🧠 **Application** - Regras de Negócio

Contém a lógica de negócio pura, independente de frameworks e tecnologias externas.

```
src/application/
├── auth/                   # Autenticação e autorização
│   ├── interface.py        # Contratos de negócio
│   └── implementation.py   # Lógica de autenticação
├── chat/                   # Processamento de conversas
│   ├── interface.py
│   └── implementation.py   # Lógica de chat e streaming
├── log_chat/              # Histórico de conversas
│   ├── interface.py
│   ├── implementation.py  # Gerenciamento de sessões
│   ├── schemas/           # Modelos de domínio
│   └── exceptions/        # Exceções de negócio
├── llm_model/             # Gerenciamento de modelos LLM
└── user/                  # Gerenciamento de usuários
```

**Funcionalidades implementadas:**
- Autenticação JWT e OAuth2
- Chat com streaming em tempo real
- Histórico de conversas com paginação
- Gerenciamento de modelos LLM
- Soft delete de sessões

### 🌐 **Presentation** - Interface Externa

Camada responsável por expor a aplicação através de APIs REST, WebSocket e interface web.

```
src/presentation/
├── api/                    # API REST e WebSocket
│   ├── main.py            # Configuração FastAPI
│   ├── routers/           # Endpoints organizados por domínio
│   │   ├── auth.py        # Autenticação
│   │   ├── chat.py        # Chat REST
│   │   ├── websocket_chat.py  # Chat WebSocket
│   │   ├── log_chat.py    # Histórico de conversas
│   │   └── llm_models.py  # Modelos LLM
│   ├── schemas/           # Validação de entrada/saída
│   │   ├── chat.py        # Schemas de chat
│   │   └── log_chat.py    # Schemas de histórico
│   └── dependencies/      # Injeção de dependências
│       ├── auth.py        # Middleware de autenticação
│       └── *_factory.py   # Factories para DI
└── web/                   # Interface Streamlit
    ├── main.py
    ├── pages/             # Páginas da aplicação
    ├── components/        # Componentes reutilizáveis
    └── contexts/          # Gerenciamento de estado
```

**APIs disponíveis:**

#### Autenticação
- `GET /api/v1/auth/login` - Iniciar login OAuth2 com Google
- `GET /api/v1/auth/google/callback` - Callback OAuth2 do Google
- `GET /api/v1/auth/me` - Informações do usuário autenticado
- `POST /api/v1/auth/logout` - Logout do usuário

#### Chat
- `POST /api/v1/chat/` - Chat completo (não-streaming)
- `WebSocket /api/v1/chat/ws` - Chat em tempo real com streaming

#### Histórico de Conversas
- `GET /api/v1/log-chat/sessions` - Listar sessões com histórico e paginação
- `GET /api/v1/log-chat/sessions/{id}` - Buscar sessão específica
- `PATCH /api/v1/log-chat/sessions/{id}/title` - Editar título da sessão
- `DELETE /api/v1/log-chat/sessions/{id}` - Soft delete da sessão

#### Modelos LLM
- `GET /api/v1/llm-models/` - Listar modelos LLM disponíveis
- `GET /api/v1/llm-models/{id}` - Buscar modelo específico

#### Utilitários
- `GET /` - Informações básicas da API
- `GET /health` - Health check da aplicação

### 🔧 **Shared Kernel** - Módulos Compartilhados

Componentes utilizados por todas as camadas da aplicação.

```
src/shared_kernel/
├── config.py              # Configurações centralizadas
├── schemas/               # Modelos de domínio compartilhados
│   └── message.py         # Schema de mensagem
└── utils/                 # Utilitários
    └── jwt_utils.py       # Funções JWT
```

### 🏗️ **Infrastructure** - Infraestrutura

```
src/infrastructure/
└── database/
    ├── models/           # Modelos SQLAlchemy
    │   ├── user.py      # Usuários
    │   ├── chat_session.py # Sessões de chat
    │   ├── chat_message.py # Mensagens
    │   └── llm_model.py # Modelos LLM
    ├── migrations/      # Migrações Alembic
    ├── seeders/         # Dados iniciais
    ├── base.py          # SQLAlchemy Base
    └── session.py       # Session Management
```

**Funcionalidades:**
- Soft delete implementado
- Timestamps automáticos
- Relacionamentos bem definidos
- Migrações versionadas
- Arquitetura organizada em camadas

## ⚠️ Importante para autenticação Google:
Se você for executar a aplicação em um ambiente diferente do local (produção, staging, etc.),
é necessário adicionar a URL de redirecionamento no console do Google Cloud Platform:
1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Vá para "APIs & Services" > "Credentials"
3. Edite o OAuth 2.0 Client ID
4. Adicione a URL de redirecionamento em "Authorized redirect URIs"
   - Exemplo: `https://seu-dominio.com/api/v1/auth/google/callback`


## Comunicação WebSocket

### Conectar ao Chat

```
WebSocket: ws://localhost:8501/api/v1/chat/ws?token=<jwt_token>&client_id=<id>
```

### Enviar Mensagem

```json
{
  "message": "sua mensagem aqui",
  "model_id": 1
}
```

### Parar Streaming

```json
{
  "action": "stop_stream"
}
```

### Respostas do Servidor

```json
// Conexão estabelecida
{
  "type": "connection",
  "status": "connected",
  "user": {...}
}

// Chunk de resposta
{
  "type": "stream_chunk",
  "chunk": "texto parcial"
}

// Streaming completo
{
  "type": "stream_complete",
  "message": "resposta completa"
}
```

## Padrões Arquiteturais

### Dependency Injection
- Factories para criação de dependências
- Injeção via FastAPI Depends
- Isolamento de responsabilidades

### Repository Pattern
- Implementado nos modelos de aplicação
- Abstração da camada de dados
- Facilita testes unitários

### Factory Pattern
- `AuthApplicationFactory`
- `UserApplicationFactory`
- `LogChatApplicationFactory`

### Strategy Pattern
- Diferentes providers de LLM
- Múltiplos adapters de autenticação
- Flexibilidade para novos provedores