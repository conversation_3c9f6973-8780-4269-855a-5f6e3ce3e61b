"""
Validadores customizados para os schemas de chat
"""
from typing import List
from pydantic import BaseModel, validator
from src.presentation.api.schemas.chat import MessageRequestSchema, MessageSchema


class ChatValidators:
    """Classe com validadores reutilizáveis para chat"""

    @staticmethod
    def validate_conversation_flow(messages: List) -> List:
        """
        Valida o fluxo da conversa
        """
        if not messages:
            raise ValueError("Pelo menos uma mensagem é obrigatória")
        
        # Verificar alternância de papéis
        for i in range(1, len(messages)):
            if messages[i].role == messages[i-1].role:
                # Permitir múltiplas mensagens consecutivas do usuário
                if messages[i].role == 'ai':
                    raise ValueError("Não pode haver mensagens consecutivas da IA")
        
        # Última mensagem deve ser do usuário
        if messages[-1].role != 'user':
            raise ValueError("A última mensagem deve ser do usuário")
        
        return messages
