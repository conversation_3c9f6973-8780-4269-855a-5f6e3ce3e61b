from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional

from src.application.llm_model.implementation import LLMModelApplication
from src.presentation.api.schemas.llm_model import LLMModelResponse, LLMModelsListResponse
from src.presentation.api.dependencies.auth import get_current_user
from src.presentation.api.dependencies.llm_model_factory import get_llm_model_application
from src.adapters.repositories.user.models.user import User

router = APIRouter(prefix="/llm-models", tags=["LLM Models"])


@router.get("/", response_model=LLMModelsListResponse)
async def get_llm_models(
    active_only: bool = Query(True, description="Filtrar apenas modelos ativos"),
    current_user: User = Depends(get_current_user),
    llm_service: LLMModelApplication = Depends(get_llm_model_application)
):
    try:
        models = await llm_service.get_all_models(active_only=active_only)
        
        model_responses = [LLMModelResponse.from_orm(model) for model in models]
        
        return LLMModelsListResponse(
            models=model_responses,
            total=len(model_responses)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor: {str(e)}")


@router.get("/{model_id}", response_model=LLMModelResponse)
async def get_llm_model_by_id(
    model_id: int,
    current_user: User = Depends(get_current_user),
    llm_service: LLMModelApplication = Depends(get_llm_model_application)
):
    try:
        model = await llm_service.get_model_by_id(model_id)
        
        if not model:
            raise HTTPException(status_code=404, detail="Modelo LLM não encontrado")
        
        return LLMModelResponse.from_orm(model)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor: {str(e)}")
