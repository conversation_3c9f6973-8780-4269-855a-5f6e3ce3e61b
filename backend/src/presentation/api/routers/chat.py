from fastapi import APIRouter, WebSocket,Depends, WebSocketDisconnect, Query, HTTPException
from typing import Dict, Any
import json
import asyncio

from src.presentation.api.schemas.chat import SimpleChatRequestSchema, StopStreamSchema
from src.presentation.api.dependencies.chat_factory import ChatDependencies, get_chat_dependencies
from src.application.chat.implementation import ChatApplication

router = APIRouter(prefix="/chat", tags=["WebSocket Chat"])

class ChatManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.streaming_tasks: Dict[str, asyncio.Task] = {}
        self.stop_events: Dict[str, asyncio.Event] = {}
        self.force_stop: Dict[str, bool] = {}
        self.chat_sessions: Dict[str, int] = {}  # client_id -> session_id mapping
        self.user_ids: Dict[str, int] = {}  # client_id -> user_id mapping
        self.log_chat_instances: Dict[str, 'ChatHistoryApplication'] = {}  # client_id -> log_chat instance

    async def connect(self, websocket: WebSocket, client_id: str, user_id: int, dependencies: ChatDependencies, session_id: int = None):
        # Verificar se já existe uma conexão ativa para este client_id
        if client_id in self.active_connections:
            await self._disconnect_existing_client(client_id)
        
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.user_ids[client_id] = user_id
        self.stop_events[client_id] = asyncio.Event()
        self.force_stop[client_id] = False

        self.log_chat_instances[client_id] = dependencies.chat_history_app
        log_chat = self.log_chat_instances[client_id]

        if session_id:
            # Use existing session
            chat_session = log_chat.get_session_by_id(session_id, user_id)
            if chat_session and chat_session.user_id == user_id:
                self.chat_sessions[client_id] = session_id
                # Update last_interaction when reconnecting to existing session
                log_chat.update_session_interaction(session_id, user_id)

    async def create_session_for_first_message(self, client_id: str, first_message: str) -> int:
        """
        Create a new session with auto-generated title based on first message

        Args:
            client_id: WebSocket client ID
            first_message: First message content to generate title from

        Returns:
            Session ID of the created session
        """
        if client_id not in self.user_ids or client_id not in self.log_chat_instances:
            raise Exception("Client not properly connected")

        user_id = self.user_ids[client_id]
        log_chat = self.log_chat_instances[client_id]

        # Create session with generated title
        chat_session = await log_chat.create_session_with_generated_title(user_id, first_message)
        self.chat_sessions[client_id] = chat_session.session_id

        return chat_session.session_id

    def disconnect(self, client_id: str):
        # Finalize chat session
        if client_id in self.chat_sessions:
            session_id = self.chat_sessions[client_id]
            if client_id in self.log_chat_instances:
                user_id = self.user_ids.get(client_id)
                log_chat = self.log_chat_instances[client_id]
                if user_id:
                    log_chat.finalize_websocket_session(session_id, user_id)
                del self.log_chat_instances[client_id]
            del self.chat_sessions[client_id]

        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.user_ids:
            del self.user_ids[client_id]
        if client_id in self.streaming_tasks:
            del self.streaming_tasks[client_id]
        if client_id in self.stop_events:
            del self.stop_events[client_id]

    async def send_message(self, client_id: str, message: Dict[str, Any]):
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_json(message)

    def stop_streaming(self, client_id: str):
        """Para o streaming atual para um cliente"""
        if client_id in self.stop_events:
            self.stop_events[client_id].set()

        self.force_stop[client_id] = True

        if client_id in self.streaming_tasks:
            task = self.streaming_tasks[client_id]
            if not task.done():
                task.cancel()
            del self.streaming_tasks[client_id]

    def is_streaming_stopped(self, client_id: str) -> bool:
        """Verifica se o streaming foi parado"""
        event_stopped = client_id in self.stop_events and self.stop_events[client_id].is_set()
        force_stopped = self.force_stop.get(client_id, False)
        return event_stopped or force_stopped

    def reset_stop_flag(self, client_id: str):
        """Reseta o event de parada para permitir novo streaming"""
        if client_id in self.stop_events:
            self.stop_events[client_id].clear()  # Limpar o event
        self.force_stop[client_id] = False  # Resetar flag forçada também

    async def _disconnect_existing_client(self, client_id: str):
        """
        Encerra conexão WebSocket anterior do mesmo cliente
        """
        try:
            # Parar streaming ativo
            if client_id in self.streaming_tasks:
                task = self.streaming_tasks[client_id]
                if not task.done():
                    task.cancel()
            
            # Enviar mensagem de desconexão para o cliente anterior
            if client_id in self.active_connections:
                old_websocket = self.active_connections[client_id]
                try:
                    await old_websocket.send_json({
                        "type": "connection_replaced",
                        "message": "Nova conexão detectada. Esta sessão será encerrada."
                    })
                    await old_websocket.close(code=4002, reason="Connection replaced")
                except Exception:
                    # Ignorar erros se a conexão já estiver fechada
                    pass
            
            # Usar método disconnect() existente para limpeza completa
            self.disconnect(client_id)
            
        except Exception as e:
            print(f"Erro ao desconectar cliente anterior {client_id}: {str(e)}")


manager = ChatManager()


@router.websocket("/ws")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    client_id: str = Query(None),
    session_id: int = Query(None),
    dependencies: ChatDependencies = Depends(get_chat_dependencies)
):
    if not client_id:
        client_id = f"client_{id(websocket)}"

    try:
        # Authenticate user using dependencies
        current_user = await dependencies.authenticate_user(token)

        await manager.connect(websocket, client_id, current_user['user']['id'], dependencies, session_id)
         
        # Get session info for connection message
        current_session_id = manager.chat_sessions.get(client_id)
        session_info = "aguardando primeira mensagem"
        if session_id and current_session_id == session_id:
            session_info = f"sessão existente #{session_id}"
        elif current_session_id:
            session_info = f"sessão #{current_session_id}"

        await manager.send_message(client_id, {
            "type": "connection",
            "status": "connected",
            "message": f"Conectado ao chat! Usuário: {current_user['user']['email']} - {session_info}",
            "user": current_user['user'],
            "session_id": current_session_id,
            "formats": {
                "chat": {"message": "sua mensagem", "model_id": 1},
                "stop": {"action": "stop_stream"}
            }
        })
        while True:
            try:
                data = await websocket.receive_json()
            except Exception:
                break


            if isinstance(data, dict) and "action" in data and data["action"] == "stop_stream":
                manager.stop_streaming(client_id)
                await asyncio.sleep(0.1)
                await manager.send_message(client_id, {
                    "type": "stream_stopped",
                    "message": "Streaming interrompido pelo usuário"
                })
                continue

            # Validar como mensagem de chat
            try:
                chat_request = SimpleChatRequestSchema(**data)
            except Exception as e:
                await manager.send_message(client_id, {
                    "type": "error",
                    "message": f"Formato inválido. Use: {{\"message\": \"sua mensagem\", \"model_id\": 1}} ou {{\"action\": \"stop_stream\"}}. Erro: {str(e)}"
                })
                continue
            
            manager.reset_stop_flag(client_id)

            # Check if session exists, if not create one with generated title
            current_session_id = manager.chat_sessions.get(client_id)
            if not current_session_id:
                try:
                    current_session_id = await manager.create_session_for_first_message(client_id, chat_request.message)
                    await manager.send_message(client_id, {
                        "type": "session_created",
                        "session_id": current_session_id,
                        "message": f"Nova sessão criada #{current_session_id}"
                    })
                except Exception as e:
                    await manager.send_message(client_id, {
                        "type": "error",
                        "message": f"Erro ao criar sessão: {str(e)}"
                    })
                    continue

            async def send_message_callback(message: Dict[str, Any]):
                if manager.is_streaming_stopped(client_id) and message.get("type") == "stream_chunk":
                    return
                await manager.send_message(client_id, message)

            def is_stopped_callback() -> bool:
                return manager.is_streaming_stopped(client_id)

            # Log user message (será feito após processar arquivos se houver)

            # Processar arquivos anexados se houver
            enhanced_message = chat_request.message
            files_processed = []
            attached_files_info = []  # Para salvar info de todos os arquivos na mensagem

            if chat_request.file_ids:
                from src.presentation.api.dependencies.file_upload_factory import get_file_upload_application
                file_service = get_file_upload_application()

                # Get model provider using dependencies
                provider = await dependencies.get_model_provider(chat_request.model_id)

                for file_id in chat_request.file_ids:
                    try:
                        # Obter informações do arquivo antes de processar
                        file_info = await file_service.get_file_info(file_id)
                        if not file_info:
                            raise ValueError(f"File not found: {file_id}")

                        # Adicionar info do arquivo para logging
                        attached_files_info.append({
                            "name": file_info.original_filename,
                            "mime_type": file_info.mime_type
                        })

                        # Processar arquivo para LLM com provider correto
                        file_content = await file_service.process_file_for_llm(file_id, chat_request.message, provider)

                        # Substituir mensagem com conteúdo multimodal (apenas no primeiro arquivo)
                        if enhanced_message == chat_request.message:
                            enhanced_message = file_content
                        files_processed.append({
                            "file_id": file_id,
                            "status": "success",
                            "filename": file_info.original_filename,
                            "mime_type": file_info.mime_type
                        })

                        # Enviar status de processamento
                        await manager.send_message(client_id, {
                            "type": "file_processed",
                            "file_id": file_id,
                            "filename": file_info.original_filename,
                            "status": "success"
                        })

                    except Exception as e:
                        files_processed.append({"file_id": file_id, "status": "error", "error": str(e)})
                        await manager.send_message(client_id, {
                            "type": "file_error",
                            "file_id": file_id,
                            "error": str(e)
                        })

                # Enviar resumo do processamento
                await manager.send_message(client_id, {
                    "type": "files_summary",
                    "total_files": len(chat_request.file_ids),
                    "successful": len([f for f in files_processed if f["status"] == "success"]),
                    "failed": len([f for f in files_processed if f["status"] == "error"])
                })

            # Logar mensagem do usuário com informações dos arquivos (se houver)
            if client_id in manager.chat_sessions and client_id in manager.log_chat_instances:
                session_id = manager.chat_sessions[client_id]
                user_id = manager.user_ids.get(client_id)
                log_chat = manager.log_chat_instances[client_id]
                chat_session = log_chat.get_session_by_id(session_id, user_id)
                if chat_session:
                    from src.shared_kernel.schemas.message import Message
                    user_message = Message(role="user", content=chat_request.message)

                    # Logar mensagem com array de arquivos (se houver)
                    log_chat.log_message(
                        chat_session,
                        user_message,
                        model_id=chat_request.model_id,
                        attached_files=attached_files_info if attached_files_info else None
                    )

            # Iniciar streaming em background task
            async def process_chat():
                chat_app = ChatApplication(llm_provider=None)

                # Wrapper para logar resposta do bot
                original_callback = send_message_callback
                bot_response_content = ""

                async def logging_callback(message: Dict[str, Any]):
                    nonlocal bot_response_content

                    try:
                        # Acumular conteúdo da resposta do bot
                        if message.get("type") == "stream_chunk":
                            bot_response_content += message.get("chunk", "")
                        elif message.get("type") == "stream_complete":
                            # Use final_response from ApplicationChat or fallback to accumulated content
                            final_content = message.get("final_response", bot_response_content)

                            # Log final response
                            if client_id in manager.chat_sessions and client_id in manager.log_chat_instances:
                                session_id = manager.chat_sessions[client_id]
                                user_id = manager.user_ids.get(client_id)
                                log_chat = manager.log_chat_instances[client_id]
                                chat_session = log_chat.get_session_by_id(session_id, user_id)
                                if chat_session:
                                    from src.shared_kernel.schemas.message import Message
                                    # Use final_response if available, otherwise use accumulated content
                                    content_to_log = final_content if final_content else bot_response_content
                                    bot_message = Message(role="assistant", content=content_to_log)
                                    log_chat.log_message(chat_session, bot_message, model_id=chat_request.model_id)
                        elif message.get("type") == "stream_interrupted":
                            # Salvar mensagem parcial quando streaming é interrompido
                            partial_content = message.get("partial_response", bot_response_content)

                            # Log partial response as a complete message
                            if partial_content and client_id in manager.chat_sessions and client_id in manager.log_chat_instances:
                                session_id = manager.chat_sessions[client_id]
                                user_id = manager.user_ids.get(client_id)
                                log_chat = manager.log_chat_instances[client_id]
                                chat_session = log_chat.get_session_by_id(session_id, user_id)
                                if chat_session:
                                    from src.shared_kernel.schemas.message import Message
                                    bot_message = Message(role="assistant", content=partial_content)
                                    log_chat.log_message(chat_session, bot_message, model_id=chat_request.model_id)
                    except Exception:
                        # Log error silently without debug prints
                        pass

                    await original_callback(message)

                # Obter session_id e log_chat para contexto
                current_session_id = manager.chat_sessions.get(client_id)
                current_log_chat = manager.log_chat_instances.get(client_id)

                # Processar mensagens com arquivos diretamente

                if chat_request.file_ids and enhanced_message != chat_request.message:

                    # Enviar mensagem de debug
                    await logging_callback({
                        "type": "debug",
                        "message": "Iniciando processamento de arquivo..."
                    })

                    # Modificar o chat_request para incluir conteúdo multimodal
                    from src.presentation.api.schemas.chat import SimpleChatRequestSchema

                    # Criar novo request com conteúdo multimodal
                    modified_request = SimpleChatRequestSchema(
                        message=enhanced_message,  # Usar conteúdo multimodal
                        model_id=chat_request.model_id,
                        file_ids=[]  # Limpar para evitar reprocessamento
                    )

                    # Processar usando o fluxo normal do ChatApplication
                    await chat_app.process_websocket_chat(
                        chat_request=modified_request,
                        model_service=dependencies.llm_model_app,
                        send_message_callback=logging_callback,
                        is_stopped_callback=is_stopped_callback,
                        log_chat_service=current_log_chat,
                        session_id=current_session_id
                    )
                else:
                    # Processar mensagem normal sem arquivos
                    await chat_app.process_websocket_chat(
                        chat_request=chat_request,
                        model_service=dependencies.llm_model_app,
                        send_message_callback=logging_callback,
                        is_stopped_callback=is_stopped_callback,
                        log_chat_service=current_log_chat,
                        session_id=current_session_id
                    )



            # Criar task em background - NÃO aguardar aqui
            streaming_task = asyncio.create_task(process_chat())
            manager.streaming_tasks[client_id] = streaming_task

            # Não aguardar a task - deixar rodar em background
            # O loop principal continua processando comandos

    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except HTTPException as e:
        await websocket.close(code=4001, reason=e.detail)
        manager.disconnect(client_id)
    except Exception as e:
        print(f"WebSocket error: {str(e)}")
        try:
            if client_id in manager.active_connections:
                await manager.send_message(client_id, {
                    "type": "error",
                    "message": f"Erro interno: {str(e)}"
                })
            else:
                await websocket.close(code=4000, reason=f"Error: {str(e)}")
        except:
            pass
        manager.disconnect(client_id)
    finally:
        # Cleanup: cancelar task de streaming se ainda estiver rodando
        if client_id in manager.streaming_tasks:
            task = manager.streaming_tasks[client_id]
            if not task.done():
                task.cancel()
            del manager.streaming_tasks[client_id]
