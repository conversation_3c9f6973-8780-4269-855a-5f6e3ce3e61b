from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any

from src.presentation.api.dependencies.auth import get_current_user
from src.presentation.api.dependencies.chat_history_factory import get_chat_history_application
from src.presentation.api.schemas.chat_history import (
    PaginatedLogChatSessionsResponse,
    LogChatSessionResponse,
    LogChatMessageResponse,
    UpdateSessionTitleRequest
)
from src.application.chat_history.implementation import ChatHistoryApplication

router = APIRouter(prefix="/log-chat", tags=["Log Chat"])


@router.get("/sessions", response_model=PaginatedLogChatSessionsResponse)
async def get_user_chat_sessions(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    log_chat: ChatHistoryApplication = Depends(get_chat_history_application)
):
    """
    Busca as sessões de chat do usuário com histórico de mensagens e paginação
    """
    try:
        user_id = current_user['user']['id']

        # Buscar sessões com histórico
        result = log_chat.get_user_sessions_with_history(
            user_id=user_id,
            page=page,
            per_page=per_page
        )
            
        # Converter para response model
        sessions_response = []
        for session in result.sessions:
            messages_response = [
                LogChatMessageResponse(
                    id=msg.id,
                    author=msg.author,
                    content=msg.content,
                    timestamp=msg.timestamp,
                    model_id=msg.model_id,
                    attached_files=msg.attached_files
                )
                for msg in session.messages or []
            ]

            session_response = LogChatSessionResponse(
                session_id=session.session_id,
                user_id=session.user_id,
                title=session.title,
                first_interaction=session.first_interaction,
                last_interaction=session.last_interaction,
                messages=messages_response
            )
            sessions_response.append(session_response)

        return PaginatedLogChatSessionsResponse(
            sessions=sessions_response,
            total=result.total,
            page=result.page,
            per_page=result.per_page,
            total_pages=result.total_pages
        )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


@router.get("/sessions/{session_id}", response_model=LogChatSessionResponse)
async def get_chat_session_by_id(
    session_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    log_chat: ChatHistoryApplication = Depends(get_chat_history_application)
):
    """
    Busca uma sessão específica com histórico de mensagens
    """
    try:
        user_id = current_user['user']['id']

        # Buscar sessão específica com mensagens
        session = log_chat.retrieve_session_with_messages(user_id, str(session_id))

        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")

        # Converter mensagens para response model
        messages_response = [
            LogChatMessageResponse(
                id=msg.id,
                author=msg.author,
                content=msg.content,
                timestamp=msg.timestamp,
                model_id=msg.model_id,
                attached_files=msg.attached_files
            )
            for msg in session.messages or []
        ]

        return LogChatSessionResponse(
            session_id=session.session_id,
            user_id=session.user_id,
            title=session.title,
            first_interaction=session.first_interaction,
            last_interaction=session.last_interaction,
            messages=messages_response
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


@router.patch("/sessions/{session_id}/title", response_model=LogChatSessionResponse)
async def update_session_title(
    session_id: int,
    request: UpdateSessionTitleRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    log_chat: ChatHistoryApplication = Depends(get_chat_history_application)
):
    """
    Atualiza o título de uma sessão específica
    """
    try:
        user_id = current_user['user']['id']

        # Atualizar título da sessão
        success = log_chat.update_session_title(user_id, session_id, request.title)

        if not success:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")

        # Buscar sessão atualizada
        session = log_chat.retrieve_session(user_id, str(session_id))

        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")

        return LogChatSessionResponse(
            session_id=session.session_id,
            user_id=session.user_id,
            title=session.title,
            first_interaction=session.first_interaction,
            last_interaction=session.last_interaction,
            messages=[]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")


@router.delete("/sessions/{session_id}")
async def soft_delete_session(
    session_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    log_chat: ChatHistoryApplication = Depends(get_chat_history_application)
):
    """
    Faz soft delete de uma sessão específica
    """
    try:
        user_id = current_user['user']['id']

        # Fazer soft delete da sessão
        success = log_chat.soft_delete_session(user_id, session_id)

        if not success:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")

        return {"message": "Sessão deletada com sucesso"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}")
