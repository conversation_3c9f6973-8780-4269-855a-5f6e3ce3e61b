from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from src.infrastructure.database.session import get_db_session
from src.presentation.api.dependencies.auth_factory import AuthApplicationFactory
from src.application.auth import AuthApplication
from src.presentation.api.dependencies.auth import get_current_user

router = APIRouter(prefix="/auth", tags=["Authentication"])

def get_auth_application(db: Session = Depends(get_db_session)):
    return AuthApplicationFactory.create_google_auth_application(db)

@router.get("/google/login")
async def google_login(auth_app: AuthApplication = Depends(get_auth_application)):
    try:
        authorization_url = await auth_app.get_login_url()
        return {
            "authorization_url": authorization_url,
            "message": "Acesse a URL para fazer login com Google"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar URL de autorização: {str(e)}"
        )

@router.get("/google/callback")
async def google_callback(
    code: str,
    auth_app: AuthApplication = Depends(get_auth_application),
    error: str = None
):
    if error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Erro na autorização do Google: {error}"
        )
    
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Código de autorização não fornecido"
        )

    try:
        # Processa o callback
        user, jwt_token, picture = await auth_app.process_callback(code)
        print("access_token", jwt_token)
        return {
            "access_token": jwt_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "sub": user.google_sub,
                "picture": picture
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro no processo de autenticação: {str(e)}"
        )

@router.get("/me")
async def get_current_user_info(current_user = Depends(get_current_user)):
    return {
        "user": current_user["user"],
        "payload": current_user["payload"]
    }

@router.post("/logout")
async def logout(current_user = Depends(get_current_user)):
    return {
        "message": "Logout realizado com sucesso",
        "user": current_user["user"]["email"]
    }

