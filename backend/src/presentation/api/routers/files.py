"""
File Upload API Router
"""

from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status
from fastapi.responses import JSONResponse
from typing import List, Dict, Any

from src.application.file_upload.interface import FileUploadApplicationInterface
from src.presentation.api.dependencies.file_upload_factory import get_file_upload_application
from src.presentation.api.dependencies.auth import get_current_user
from src.presentation.api.schemas.file_upload import (
    FileUploadResponse,
    FileInfoResponse, 
    FileListResponse,
    FileStatsResponse,
    CleanupResponse,
    ErrorResponse,
    SupportedTypesResponse
)

router = APIRouter(prefix="/api/v1/files", tags=["files"])


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Upload a file for processing with LLM

    - **file**: File to upload (PDF, images, text files)
    - **Authentication**: Requires valid JWT token
    - Returns file ID and metadata for use in chat
    """
    try:
        # Read file content
        file_content = await file.read()
        
        # Get MIME type
        mime_type = file.content_type or "application/octet-stream"
        
        # Upload file
        temp_file = await file_service.upload_file(
            file_content=file_content,
            filename=file.filename or "unnamed_file",
            mime_type=mime_type
        )
        
        return FileUploadResponse(
            file_id=temp_file.file_id,
            filename=temp_file.original_filename,
            file_type=temp_file.mime_type,
            file_size=temp_file.file_size,
            upload_timestamp=temp_file.upload_timestamp,
            expiry_timestamp=temp_file.expiry_timestamp
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/info/{file_id}", response_model=FileInfoResponse)
async def get_file_info(
    file_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Get information about an uploaded file

    - **file_id**: Unique file identifier
    - **Authentication**: Requires valid JWT token
    - Returns file metadata and status
    """
    try:
        temp_file = await file_service.get_file_info(file_id)
        
        if not temp_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}"
            )
        
        return FileInfoResponse(
            file_id=temp_file.file_id,
            filename=temp_file.original_filename,
            file_type=temp_file.mime_type,
            file_size=temp_file.file_size,
            upload_timestamp=temp_file.upload_timestamp,
            expiry_timestamp=temp_file.expiry_timestamp,
            is_expired=temp_file.is_expired(),
            is_valid_for_llm=temp_file.is_valid_for_llm()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Delete an uploaded file
    
    - **file_id**: Unique file identifier
    - Returns success status
    """
    try:
        success = await file_service.delete_file(file_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found or could not be deleted: {file_id}"
            )
        
        return {"message": f"File {file_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/list", response_model=FileListResponse)
async def list_files(
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    List all uploaded files
    
    - Returns list of all files with metadata
    """
    try:
        files = await file_service.list_files()
        
        file_responses = [
            FileInfoResponse(
                file_id=f.file_id,
                filename=f.original_filename,
                file_type=f.mime_type,
                file_size=f.file_size,
                upload_timestamp=f.upload_timestamp,
                expiry_timestamp=f.expiry_timestamp,
                is_expired=f.is_expired(),
                is_valid_for_llm=f.is_valid_for_llm()
            )
            for f in files
        ]
        
        return FileListResponse(
            files=file_responses,
            total_count=len(file_responses)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/cleanup", response_model=CleanupResponse)
async def cleanup_expired_files(
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Clean up expired files
    
    - Removes all expired files from storage
    - Returns number of files cleaned up
    """
    try:
        cleaned_count = await file_service.cleanup_expired_files()
        
        return CleanupResponse(
            cleaned_files=cleaned_count,
            message=f"Successfully cleaned up {cleaned_count} expired files"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/stats", response_model=FileStatsResponse)
async def get_file_stats(
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Get file upload statistics
    
    - Returns statistics about uploaded files
    """
    try:
        stats = await file_service.get_file_stats()
        
        return FileStatsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/supported-types", response_model=SupportedTypesResponse)
async def get_supported_types(
    current_user: Dict[str, Any] = Depends(get_current_user),
    file_service: FileUploadApplicationInterface = Depends(get_file_upload_application)
):
    """
    Get supported file types
    
    - Returns list of supported MIME types and descriptions
    """
    try:
        supported_types = file_service.get_supported_types()
        
        # Create descriptions
        descriptions = {
            'application/pdf': 'PDF Document',
            'image/jpeg': 'JPEG Image',
            'image/png': 'PNG Image',
            'image/gif': 'GIF Image',
            'image/webp': 'WebP Image',
            'text/plain': 'Text File',
            'text/markdown': 'Markdown File',
            'application/json': 'JSON File',
            'text/csv': 'CSV File'
        }
        
        return SupportedTypesResponse(
            supported_types=supported_types,
            descriptions=descriptions,
            max_file_size=20 * 1024 * 1024,  # 20MB
            expiry_hours=1
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )
