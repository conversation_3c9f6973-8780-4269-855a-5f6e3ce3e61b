from sqlalchemy.orm import Session
from src.application.auth.implementation import AuthApplication
from src.application.auth.interface import AuthApplicationInterface
from src.application.user.implementation import UserApplication
from src.adapters.auth.provider.google import GoogleAuthAdapter
from src.adapters.repositories.user.repository import UserRepository


class AuthApplicationFactory:

    @staticmethod
    def create_google_auth_application(db: Session) -> AuthApplicationInterface:
        # Criar repository adapter
        user_repo = UserRepository(db)

        # Criar user application com repository
        user_app = UserApplication(user_repo)

        # Criar auth adapter
        auth_adapter = GoogleAuthAdapter()

        return AuthApplication(auth_adapter, user_app)


