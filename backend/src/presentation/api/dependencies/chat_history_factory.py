from fastapi import Depends
from sqlalchemy.orm import Session

from src.infrastructure.database.session import get_db_session
from src.application.chat_history.implementation import ChatHistoryApplication
from src.adapters.repositories.chat_history.repository import ChatSessionRepository, ChatMessageRepository


def get_chat_history_application(
    db: Session = Depends(get_db_session)
) -> ChatHistoryApplication:
    chat_session_repo = ChatSessionRepository(db)
    chat_message_repo = ChatMessageRepository(db)
    return ChatHistoryApplication(chat_session_repo, chat_message_repo)
