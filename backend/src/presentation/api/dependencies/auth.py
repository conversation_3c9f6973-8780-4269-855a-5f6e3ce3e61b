from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from src.shared_kernel.config import Config
from src.infrastructure.database.session import get_db_session
from src.adapters.repositories.user.models.user import User

security = HTTPBearer(auto_error=False)

def verify_token(token: str) -> Dict[str, Any]:
    try:
        payload = jwt.decode(
            token, 
            Config.JWT.SECRET_KEY, 
            algorithms=[Config.JWT.ALGORITHM]
        )
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token inválido",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def _get_user_from_token(token: str, db: Session) -> Dict[str, Any]:
    """
    Função comum para validar token e buscar usuário no banco.
    Evita duplicação de código entre get_current_user e get_current_user_websocket.
    """
    payload = verify_token(token)
    
    user_sub: str = payload.get("sub")
    
    if not user_sub:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token inválido: sub não encontrado"
        )
        
    user = db.query(User).filter(User.google_sub == user_sub).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Usuário não encontrado"
        )
    
    return {
        "user": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "sub": user.google_sub,
            "picture": payload.get("picture")  # Picture vem do JWT, não do banco
        },
        "payload": payload
    }

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db_session)
):
    """
    Validação JWT para endpoints REST usando HTTPBearer
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token de autenticação necessário",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return await _get_user_from_token(credentials.credentials, db)


async def get_current_user_websocket(
    token: str,
    db: Session = Depends(get_db_session)
):
    """
    Validação JWT específica para WebSocket (recebe token diretamente)
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token de autenticação necessário"
        )
    
    return await _get_user_from_token(token, db)
