from fastapi import Depends
from sqlalchemy.orm import Session
from typing import Dict, Any

from src.infrastructure.database.session import get_db_session
from src.presentation.api.dependencies.auth import get_current_user_websocket
from src.application.llm_model.implementation import LLMModelApplication
from src.application.chat_history.implementation import ChatHistoryApplication
from src.adapters.repositories.llm_model.repository import LLMModelRepository
from src.adapters.repositories.chat_history.repository import ChatSessionRepository, ChatMessageRepository


class ChatDependencies:
    """Container for WebSocket chat dependencies with proper session management"""

    def __init__(self,
                 db_session: Session,
                 chat_history_app: ChatHistoryApplication,
                 llm_model_app: LLMModelApplication):
        self.db_session = db_session
        self.chat_history_app = chat_history_app
        self.llm_model_app = llm_model_app
    
    async def authenticate_user(self, token: str) -> Dict[str, Any]:
        """Authenticate user with WebSocket token"""
        return await get_current_user_websocket(token, self.db_session)
    
    async def get_model_provider(self, model_id: int) -> str:
        """Get model provider for file processing"""
        try:
            model = await self.llm_model_app.get_model_by_id(model_id)
            return model.provider if model else "openai"
        except Exception as e:
            print(f"Warning: Could not get model provider, using default: {e}")
            return "openai"


def get_chat_dependencies(
    db: Session = Depends(get_db_session)
) -> ChatDependencies:
    """
    Factory function to create WebSocket chat dependencies with proper session management
    
    Args:
        db: Database session from dependency injection
        
    Returns:
        ChatDependencies: Container with all required dependencies
    """
    # Create repositories and applications with the same db session
    chat_session_repo = ChatSessionRepository(db)
    chat_message_repo = ChatMessageRepository(db)
    chat_history_app = ChatHistoryApplication(chat_session_repo, chat_message_repo)
    
    llm_model_repo = LLMModelRepository(db)
    llm_model_app = LLMModelApplication(llm_model_repo)

    return ChatDependencies(
        db_session=db,
        chat_history_app=chat_history_app,
        llm_model_app=llm_model_app
    )
