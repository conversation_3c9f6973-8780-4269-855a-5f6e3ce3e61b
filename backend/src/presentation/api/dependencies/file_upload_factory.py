"""
File Upload Dependency Factory
"""

from src.application.file_upload.interface import FileUploadApplicationInterface
from src.application.file_upload.implementation import FileUploadApplication
from src.adapters.file_storage.temp_file_storage import TempFileStorageAdapter
from src.adapters.file_processor.base64_processor import Base64FileProcessor


class FileUploadApplicationFactory:
    """Factory for creating FileUploadApplication instances"""
    
    @staticmethod
    def create() -> FileUploadApplicationInterface:
        """Create FileUploadApplication with all dependencies"""
        
        # Create adapters
        file_storage = TempFileStorageAdapter(base_dir="/tmp/cognit_files")
        file_processor = Base64FileProcessor()
        
        # Create application service
        return FileUploadApplication(
            file_storage=file_storage,
            file_processor=file_processor
        )


def get_file_upload_application() -> FileUploadApplicationInterface:
    """FastAPI dependency for FileUploadApplication"""
    return FileUploadApplicationFactory.create()
