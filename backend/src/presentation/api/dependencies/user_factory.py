from fastapi import Depends
from sqlalchemy.orm import Session
from src.infrastructure.database.session import get_db_session
from src.application.user.implementation import UserApplication
from src.application.user.interface import UserApplicationInterface
from src.adapters.repositories.user.repository import UserRepository


class UserApplicationFactory:

    @staticmethod
    def create(db_session: Session) -> UserApplicationInterface:
        """Create UserApplication with provided database session"""
        user_repo = UserRepository(db_session)
        return UserApplication(user_repo)


def get_user_application(db: Session = Depends(get_db_session)) -> UserApplicationInterface:
    """FastAPI dependency that properly manages database session"""
    return UserApplicationFactory.create(db)
