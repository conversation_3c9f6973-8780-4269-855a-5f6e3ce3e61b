from fastapi import Depends
from sqlalchemy.orm import Session

from src.infrastructure.database.session import get_db_session
from src.application.llm_model.implementation import LLMModelApplication
from src.adapters.repositories.llm_model.repository import LLMModelRepository


def get_llm_model_application(
    db: Session = Depends(get_db_session)
) -> LLMModelApplication:
    llm_model_repo = LLMModelRepository(db)
    return LLMModelApplication(llm_model_repo)
