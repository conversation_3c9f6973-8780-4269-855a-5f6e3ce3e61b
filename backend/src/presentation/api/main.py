from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from src.shared_kernel.config import Config
from src.presentation.api.routers import chat, auth_router
from src.presentation.api.routers import llm_models
from src.presentation.api.routers import chat_history
from src.presentation.api.routers import files

app = FastAPI(
    title="Cognit AI API",
    description="API para o sistema Cognit AI",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=Config.CORS.ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(chat.router, prefix="/api/v1")
app.include_router(auth_router, prefix="/api/v1")
app.include_router(llm_models.router, prefix="/api/v1")
app.include_router(chat_history.router, prefix="/api/v1")
app.include_router(files.router)

@app.get("/")
async def root():
    return {"message": "Cognit AI API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}


@app.get("/health/db")
async def database_health():
    """Check database connection pool status"""
    from src.infrastructure.database.session import get_pool_status
    try:
        pool_status = get_pool_status()
        return {
            "status": "healthy",
            "pool_status": pool_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
