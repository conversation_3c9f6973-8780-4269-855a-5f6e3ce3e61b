from pydantic import BaseModel, Field, validator
from typing import List, Literal, Optional, Any, Union
from abc import ABC

class MessageRequestSchema(BaseModel):
    """Schema para mensagem individual no request"""
    role: Literal['user', 'ai'] = Field(
        default='user',
        description="Papel do autor da mensagem"
    )
    content: str = Field(
        min_length=1,
        max_length=10000,
        description="Conteúdo da mensagem"
    )
    
    @validator('content')
    def validate_content(cls, v):
        if not v.strip():
            raise ValueError('Conteúdo não pode estar vazio')
        return v.strip()

class ChatRequestSchema(BaseModel):
    """Schema para request de chat"""
    messages: List[MessageRequestSchema] = Field(
        min_items=1,
        max_items=50,
        description="Lista de mensagens da conversa"
    )
    model_id: int = Field(
        gt=0,
        description="ID do modelo LLM a ser usado"
    )
    stream: bool = Field(
        default=False,
        description="Se deve retornar response em streaming"
    )
    
    @validator('messages')
    def validate_messages(cls, v):
        if not v:
            raise ValueError('Pelo menos uma mensagem é obrigatória')
        
        # Verificar se a última mensagem é do usuário
        if v[-1].role != 'user':
            raise ValueError('A última mensagem deve ser do usuário')
        
        return v

class ChatResponseSchema(BaseModel):
    """Schema para response de chat"""
    message: str = Field(description="Resposta da IA")
    role: Literal['ai'] = Field(default='ai', description="Papel da resposta")
    model_name: str = Field(description="Nome do modelo usado")
    model_provider: str = Field(description="Provider do modelo")

class ChatStreamResponseSchema(BaseModel):
    """Schema para response de chat em streaming"""
    chunk: str = Field(description="Chunk da resposta")
    finished: bool = Field(default=False, description="Se o streaming terminou")
    model_name: Optional[str] = Field(None, description="Nome do modelo usado")


# Classe base para schemas com configurações específicas
class BaseSchema(BaseModel, ABC):
    """Classe base para todos os schemas"""

    class Config:
        """Configuração base para schemas"""
        validate_assignment = True
        extra = "forbid"  # Não permite campos extras
        use_enum_values = True


class MessageSchema(BaseSchema):
    """Schema para mensagem individual com conversão para domínio"""
    role: str = Field(pattern="^(user|ai)$", description="Papel do autor")
    content: str = Field(min_length=1, description="Conteúdo")

    def to_domain(self):
        """Converte schema para objeto de domínio"""
        from src.shared_kernel.schemas.message import Message
        return Message(role=self.role, content=self.content)

class SimpleChatRequestSchema(BaseSchema):
    """Schema simplificado para WebSocket chat - apenas o essencial"""
    message: Union[str, List[Any]] = Field(description="Mensagem do usuário (texto ou conteúdo multimodal)")
    model_id: int = Field(gt=0, description="ID do modelo LLM (obrigatório)")
    file_ids: List[str] = Field(default=[], description="Lista de IDs de arquivos anexados")

    def to_domain_messages(self, conversation_history: List = None):
        """Converte para mensagens de domínio incluindo histórico"""
        from src.shared_kernel.schemas.message import Message

        messages = []

        # Adicionar histórico se fornecido
        if conversation_history:
            messages.extend(conversation_history)

        # Adicionar nova mensagem do usuário
        messages.append(Message(role="user", content=self.message))

        return messages


class StopStreamSchema(BaseSchema):
    """Schema para parar streaming"""
    action: str = Field(pattern="^stop_stream$", description="Ação de parar streaming")

    @classmethod
    def is_stop_command(cls, data: dict) -> bool:
        """Verifica se os dados são um comando de parada"""
        return data.get("action") == "stop_stream"
class ModelValidationSchema(BaseSchema):
    """Schema para validação de modelo"""
    model_id: int = Field(gt=0)
    is_active: bool = Field(default=True)
    provider: str = Field(min_length=1)
    name: str = Field(min_length=1)

    @validator('provider')
    def validate_provider(cls, v):
        supported_providers = ['openai', 'anthropic', 'google']
        if v.lower() not in supported_providers:
            raise ValueError(f"Provider não suportado. Use: {', '.join(supported_providers)}")
        return v.lower()

    def is_supported(self) -> bool:
        """Verifica se o provider é suportado"""
        return self.provider in ['openai', 'anthropic']

