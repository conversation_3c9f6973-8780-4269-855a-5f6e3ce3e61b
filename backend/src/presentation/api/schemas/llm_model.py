from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal


class LLMModelResponse(BaseModel):
    """Schema para resposta do modelo LLM"""
    id: int
    name: str
    provider: str
    is_active: bool
    base_url: Optional[str] = None
    max_tokens: Optional[int] = None
    context_window: Optional[int] = None
    price_type: Optional[str] = None
    price_usd: Optional[Decimal] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LLMModelsListResponse(BaseModel):
    """Schema para lista de modelos LLM"""
    models: list[LLMModelResponse]
    total: int
