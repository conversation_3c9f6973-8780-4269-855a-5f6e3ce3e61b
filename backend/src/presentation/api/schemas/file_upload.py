"""
File Upload API Schemas
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime


class FileUploadResponse(BaseModel):
    """Response schema for file upload"""
    
    file_id: str = Field(..., description="Unique file identifier")
    filename: str = Field(..., description="Original filename")
    file_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")
    upload_timestamp: datetime = Field(..., description="When file was uploaded")
    expiry_timestamp: datetime = Field(..., description="When file will expire")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class FileInfoResponse(BaseModel):
    """Response schema for file information"""
    
    file_id: str = Field(..., description="Unique file identifier")
    filename: str = Field(..., description="Original filename")
    file_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")
    upload_timestamp: datetime = Field(..., description="When file was uploaded")
    expiry_timestamp: datetime = Field(..., description="When file will expire")
    is_expired: bool = Field(..., description="Whether file has expired")
    is_valid_for_llm: bool = Field(..., description="Whether file can be processed by LLM")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class FileListResponse(BaseModel):
    """Response schema for file listing"""
    
    files: List[FileInfoResponse] = Field(..., description="List of files")
    total_count: int = Field(..., description="Total number of files")


class FileStatsResponse(BaseModel):
    """Response schema for file statistics"""
    
    total_files: int = Field(..., description="Total number of files")
    total_size_bytes: int = Field(..., description="Total size in bytes")
    expired_files: int = Field(..., description="Number of expired files")
    active_files: int = Field(..., description="Number of active files")
    by_type: Dict[str, Dict[str, int]] = Field(..., description="Statistics by MIME type")
    supported_types: List[str] = Field(..., description="List of supported MIME types")


class CleanupResponse(BaseModel):
    """Response schema for cleanup operation"""
    
    cleaned_files: int = Field(..., description="Number of files cleaned up")
    message: str = Field(..., description="Cleanup result message")


class ErrorResponse(BaseModel):
    """Response schema for errors"""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class SupportedTypesResponse(BaseModel):
    """Response schema for supported file types"""
    
    supported_types: List[str] = Field(..., description="List of supported MIME types")
    descriptions: Dict[str, str] = Field(..., description="Human-readable descriptions")
    max_file_size: int = Field(..., description="Maximum file size in bytes")
    expiry_hours: int = Field(..., description="File expiry time in hours")


# WebSocket message schemas (for integration)
class ChatMessageWithFiles(BaseModel):
    """WebSocket message schema with file attachments"""
    
    message: str = Field(..., description="User's text message")
    model_id: int = Field(..., description="LLM model ID to use")
    file_ids: List[str] = Field(default=[], description="List of file IDs to include")
    session_id: Optional[str] = Field(None, description="Chat session ID")


class FileProcessingStatus(BaseModel):
    """Status of file processing for WebSocket"""
    
    file_id: str = Field(..., description="File ID being processed")
    filename: str = Field(..., description="Original filename")
    status: str = Field(..., description="Processing status: 'processing', 'success', 'error'")
    error_message: Optional[str] = Field(None, description="Error message if status is 'error'")


class ChatResponseWithFiles(BaseModel):
    """WebSocket response schema with file processing info"""
    
    type: str = Field(..., description="Response type")
    content: Optional[str] = Field(None, description="Response content")
    files_processed: List[FileProcessingStatus] = Field(default=[], description="File processing status")
    total_files: int = Field(default=0, description="Total files in request")
    successful_files: int = Field(default=0, description="Successfully processed files")
    failed_files: int = Field(default=0, description="Failed file processing")
