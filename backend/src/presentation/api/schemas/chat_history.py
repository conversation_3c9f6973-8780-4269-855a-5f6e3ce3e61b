from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime


class LogChatMessageResponse(BaseModel):
    id: int
    author: str  # "user" or "bot"
    content: str
    timestamp: datetime
    model_id: Optional[int] = None  # ID do modelo usado
    attached_files: Optional[List[Dict[str, str]]] = None  # [{"name": "file.pdf", "mime_type": "application/pdf"}]

    class Config:
        from_attributes = True


class LogChatSessionResponse(BaseModel):
    session_id: int
    user_id: int
    title: Optional[str] = None
    first_interaction: Optional[datetime] = None
    last_interaction: Optional[datetime] = None
    messages: List[LogChatMessageResponse] = []

    class Config:
        from_attributes = True


class PaginatedLogChatSessionsResponse(BaseModel):
    sessions: List[LogChatSessionResponse]
    total: int
    page: int
    per_page: int
    total_pages: int

    class Config:
        from_attributes = True


class LogChatQueryParams(BaseModel):
    page: int = 1
    per_page: int = 10

    class Config:
        validate_assignment = True


class UpdateSessionTitleRequest(BaseModel):
    title: str

    class Config:
        validate_assignment = True
