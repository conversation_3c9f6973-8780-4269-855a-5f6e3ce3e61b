from abc import ABC
import streamlit as st


class StreamlitContext(ABC):
    def __init__(self, state_name: str):
        self.state_name = state_name
        if self.state_name not in st.session_state:
            st.session_state[self.state_name] = dict()

    def get_item(self, key, default_value=None):
        return st.session_state[self.state_name].get(key, default_value)

    def set_item(self, key, value):
        st.session_state[self.state_name][key] = value
