from src.presentation.web.interfaces.context import StreamlitContext
from abc import ABC, abstractmethod
from typing import TypeVar, Generic
import streamlit as st

T = TypeVar("T", bound=StreamlitContext)


class FieldNotInitializedException(Exception):
    pass


class StreamlitView(ABC, Generic[T]):
    def __init__(self):
        pass

    def context(self) -> T | None:
        return None

    @abstractmethod
    def render(self) -> None:
        pass

    @property
    def state(self) -> T:
        return self.context()

    @staticmethod
    def get_field(field_name: str, default_value = None) -> str:
        return st.session_state[field_name] if field_name in st.session_state else default_value

    @staticmethod
    def delete_field(field_name: str):
        del st.session_state[field_name]
