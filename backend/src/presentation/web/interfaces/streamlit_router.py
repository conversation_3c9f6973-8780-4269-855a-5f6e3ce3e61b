from src.presentation.web.interfaces.streamlit_view import StreamlitView

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Type

import streamlit as st


@dataclass
class StreamlitRoute(ABC):
    view: Type[StreamlitView]
    path: str

    title: str = None
    icon: str = None
    default: bool = False


class StreamlitRouter(StreamlitView, ABC):
    @property
    @abstractmethod
    def routes(self) -> list[StreamlitRoute]:
        return []

    def _get_current_page_by_path(self, path: str) -> StreamlitRoute | None:
        for route in self.routes:
            if route.path == path:
                return route

        return None

    def navigate(self, path: str) -> None:
        route = self._get_current_page_by_path(path)
        if route is not None:
            st.switch_page(st.Page(route.view().render, url_path=route.path, title=route.title))
        else:
            st.error("Página não encontrada")

    def render(self) -> None:
        pages = [
            st.Page(route.view().render, title=route.title, url_path=route.path, default=route.default)
            for route in self.routes
        ]

        pg = st.navigation(pages)
        pg.run()
