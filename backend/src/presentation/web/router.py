from src.presentation.web.interfaces.streamlit_router import StreamlitRouter, StreamlitRoute
from src.presentation.web.pages.chat import PageChat
from src.presentation.web.pages.home import PageHome
from src.presentation.web.pages.logout import PageLogout
from src.presentation.web.pages.sandbox import PageSandbox


class PresentationWebStreamlitRouter(StreamlitRouter):
    @property
    def routes(self) -> list[StreamlitRoute]:
        return [
            StreamlitRoute(view=PageHome, path="/", title="Home", default=True),
            StreamlitRoute(view=PageChat, path="/chat", title="Chat"),
            # StreamlitRoute(view=PageSandbox, path="/sandbox", title="Sandbox"),

            StreamlitRoute(view=PageLogout, path="/logout", title="Sair"),
        ]
