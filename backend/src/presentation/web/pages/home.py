from src.presentation.web.interfaces.streamlit_view import StreamlitView
import streamlit as st


class PageHome(StreamlitView):
    def render(self):
        st.image(
            "assets/logo_cognit.png",
            width=320,
        )

        st.write(
            "O Cognit AI é um conjunto de ferramentas internas do IEBT projetadas para facilitar "
            "as atividades diárias de trabalho, visando aumentar a produtividade da equipe."
        )

        st.write("")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.subheader("Chat")
            st.write(
                "Converse com uma IA para obter respostas rápidas e precisas. "
                "Semelhante ao ChatGPT."
            )

            if st.button("Ir para Chat", icon=":material/arrow_right_alt:"):
                from src.presentation.web.router import PresentationWebStreamlitRouter
                PresentationWebStreamlitRouter().navigate("/chat")

        with col2:
            st.subheader("Copilots")
            st.write(
                "Agentes de IA que podem ajudar a automatizar algumas tarefas repetitivas. "
            )

            if st.button("Ir para Copilot", icon=":material/arrow_right_alt:"):
                from src.presentation.web.router import PresentationWebStreamlitRouter
                PresentationWebStreamlitRouter().navigate("/copilot")
