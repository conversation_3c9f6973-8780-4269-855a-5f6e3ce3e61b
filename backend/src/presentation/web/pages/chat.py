from dataclasses import dataclass

from src.adapters.llm_chat.interface import LlmChatAdapterInterface
from src.adapters.llm_chat.provider.anthropic import LlmChatAdapterAnthropic
from src.adapters.llm_chat.provider.openai import LlmChatAdapterOpenAI
from src.application.chat import ChatApplication
from src.presentation.web.components.chat_messages import ComponentChatMessages
from src.presentation.web.contexts.chat_page import ContextChatPage
from src.presentation.web.interfaces.streamlit_view import StreamlitView

from src.shared_kernel.schemas.message import Message

import streamlit as st
import traceback


@dataclass
class LlmModel:
    name: str
    provider: LlmChatAdapterInterface
    description: str = None


@dataclass
class LlmProvider:
    name: str
    models: list[LlmModel]


class PageChat(StreamlitView[ContextChatPage]):
    def __init__(self):
        super().__init__()

        self.model_name = self.state.get_item("llm_model", "Padrão")
        self.provider_name = self.state.get_item("llm_provider")
        self.advanced = self.state.get_item("advanced", False)

    def context(self):
        return ContextChatPage()


    def render(self):
        st.title("💬 Chat")
        st.markdown(
            "Converse com a Cognit AI. "
            "Você pode fazer perguntas, pedir ajuda ou discutir ideias.",
            help=":material/check_circle: O Cognit AI já consegue:\n\n"
                 "- Conversar via texto\n"
                 "---\n\n"
                 ":material/cancel: O Cognit AI ainda não consegue:\n\n"
                 "- Ler alguns tipos de arquivo (txt e pdf)\n\n"
                 "- Gerar arquivos",
        )

        self.message_section()

        col1, col2 = st.columns([3,1], vertical_alignment="center")

        with col2:
            with st.popover(f"Alterar Modelo"):
                self.show_llm_selection()

        with col1:
            st.badge(
                f"Modelo selecionado: **{self.get_model_complete_name()}**",
                color="gray",
            )


    def show_llm_selection(self):
        self.advanced = st.toggle("Modo Avançado", False)
        self.state.set_item("advanced", self.advanced)

        if self.advanced:
            self.provider_name = st.selectbox(
                "Provedor de LLM",
                [i.name for i in self.llm_providers_options()],
            )

            provider = next(
                (i for i in self.llm_providers_options() if i.name == self.provider_name),
                None,
            )

            if provider:
                self.model_name = st.selectbox(
                    "Modelo de LLM",
                    [i.name for i in provider.models],
                )

            self.state.set_item("llm_model", self.model_name)
            self.state.set_item("llm_provider", self.provider_name)

        else:
            options: list[str] = [i.name for i in self.llm_models_options()]

            self.model_name = st.selectbox(
                "Modelo de LLM",
                options,
            )
            self.state.set_item("llm_model", self.model_name)

            pass


    def get_selected_model(self) -> LlmModel | None:
        """
        Get the selected model from the sidebar.
        """
        if self.advanced:
            provider = next(
                (i for i in self.llm_providers_options() if i.name == self.provider_name),
                None,
            )

            if provider:
                return next(
                    (i for i in provider.models if i.name == self.model_name),
                    None,
                )
        else:
            return next(
                (i for i in self.llm_models_options() if i.name == self.model_name),
                None,
            )


    def get_model_complete_name(self) -> str:
        """
        Get the complete name of the selected model.
        """
        name = self.model_name
        model = self.get_selected_model()

        if self.advanced:
            provider = next(
                (i for i in self.llm_providers_options() if i.name == self.provider_name),
                None,
            )

            if provider:
                name = f"{provider.name} - {name}"

        if model.description:
            name = f"{name} ({model.description})"

        return name


    @staticmethod
    def llm_models_options() -> list[LlmModel]:
        """
        List of available LLM models.
        """
        return [
            LlmModel(
                name="Padrão",
                description="Open AI - GPT 4o",
                provider=LlmChatAdapterOpenAI(model="gpt-4o")
            ),
            LlmModel(
                name="Tarefas Rápidas",
                description="Open AI - GPT 4o mini",
                provider=LlmChatAdapterOpenAI(model="gpt-4o-mini")
            ),
            LlmModel(
                name="Geração de Código",
                description="Anthropic - Claude 3.5 Sonnet",
                provider=LlmChatAdapterAnthropic(model="claude-3-5-sonnet-latest")
            ),
            LlmModel(
                name="Raciocínio",
                description="Open AI - GPT o1",
                provider=LlmChatAdapterOpenAI(model="gpt-o1")
            ),
        ]


    @staticmethod
    def llm_providers_options() -> list[LlmProvider]:
        """
        List of available LLM providers.
        """
        return [
            LlmProvider(
                name="OpenAI",
                models=[
                    LlmModel(name="GPT 4o", provider=LlmChatAdapterOpenAI(model="gpt-4o")),
                    LlmModel(name="GPT 4o mini", provider=LlmChatAdapterOpenAI(model="gpt-4o-mini")),
                    LlmModel(name="GPT o1", provider=LlmChatAdapterOpenAI(model="gpt-o1")),
                ]
            ),
            LlmProvider(
                name="Anthropic",
                models=[
                    LlmModel(name="Claude 3.5 Sonnet", provider=LlmChatAdapterAnthropic(model="claude-3-5-sonnet-latest")),
                    LlmModel(name="Claude 3.7 Sonnet", provider=LlmChatAdapterAnthropic(model="claude-3-7-sonnet-latest")),
                ]
            ),
            # LlmProvider(
            #     name="Google",
            #     models=[
            #         LlmModel(name="Gemini 2.5 Flash", provider=LlmChatAdapterOpenAI(model="gpt-4o-mini")),
            #     ]
            # ),
        ]


    def message_section(self):
        prompt = st.chat_input(
            "Escreva a sua mensagem",
            # accept_file="multiple",
            # file_type=["txt", "pdf", "docx"],
        )

        ComponentChatMessages(
            messages=self.state.get_messages()
        ).render()

        if prompt:
            self.state.add_message(Message(role="user", content=prompt))
            st.chat_message("human").write(prompt)

            try:
                response = self.handle_message(Message(role="user", content=prompt))
                self.state.add_message(Message(role="ai", content=response))
            except Exception as e:
                print(traceback.format_exc())

                st.error(
                    "Ocorreu um problema ao processar a mensagem. "
                    "Por favor, reporte o erro abaixo ao administrador do sistema."
                )

                with st.expander("Mensagem de erro detalhada"):
                    st.exception(e)


    def handle_message(self, message: Message):
        """
        Handle the message sent by the user.
        """
        messages = [
            *self.state.get_messages(),
            Message(role="user", content=message.content),
        ]

        application_chat = ChatApplication(
            llm_provider=self.get_selected_model().provider,
        )

        with st.chat_message("assistant"):
            response = st.write_stream(application_chat.stream(messages))

        return response
