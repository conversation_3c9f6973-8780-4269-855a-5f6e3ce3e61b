import time

from streamlit.runtime.state import QueryParamsProxy

from src.presentation.web.contexts.auth_sso_iebt import ContextAuthSsoIebt
from src.presentation.web.exceptions.user_unauthorized import UserUnauthorizedException
from src.presentation.web.interfaces.streamlit_view import StreamlitView
import streamlit as st


class PageLoginSsoIebt(StreamlitView[ContextAuthSsoIebt]):
    def context(self):
        return ContextAuthSsoIebt()

    def render(self):
        st.image(
            "assets/logo_cognit.png",
            width=320,
        )

        query_params = st.query_params
        if "token" in query_params or "code" in query_params:
            self.auth(query_params)
        else:
            self.show_login_button()


    def auth(self, query_params: QueryParamsProxy):
        try:
            with st.spinner("Autenticando..."):
                self.state.login(query_params)

            st.success("✅ Autenticação bem-sucedida!")
            time.sleep(1)

            st.query_params.clear()
            st.rerun()

        except UserUnauthorizedException:
            st.error("❌ Falha na autenticação. Tente novamente.")
            self.show_login_button()

            st.stop()


    def show_login_button(self):
        auth_url = self.state.generate_auth_url()

        st.markdown(
            f"""
            <a href="{auth_url}" target="_self" style="
                display: inline-block;
                padding: 0.5em 1em;
                background-color: #4285F4;
                color: white;
                text-decoration: none;
                border-radius: 0.3em;
                font-weight: bold;">
                🔐 Login com Google
            </a>
            """,
            unsafe_allow_html=True
        )