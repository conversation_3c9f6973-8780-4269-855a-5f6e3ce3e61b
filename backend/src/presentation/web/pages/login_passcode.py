from src.presentation.web.contexts.auth_passcode import Context<PERSON>uthPasscode
from src.presentation.web.exceptions.user_unauthorized import UserUnauthorizedException
from src.presentation.web.interfaces.streamlit_view import StreamlitView
import streamlit as st


class PageLoginPasscode(StreamlitView[ContextAuthPasscode]):
    def context(self):
        return ContextAuthPasscode()

    def render(self):
        st.image(
            "assets/logo_cognit.png",
            width=320,
        )

        st.text_input(
            "Password", type="password", on_change=self.on_password_submit, key="password",
        )

    def on_password_submit(self):
        try:
            password = self.get_field("password")
            self.state.authenticate_password(password)

        except UserUnauthorizedException:
            st.error("Erro ao autenticar. Tente novamente.")
