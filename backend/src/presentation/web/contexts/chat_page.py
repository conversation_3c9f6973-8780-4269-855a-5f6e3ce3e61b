from src.presentation.web.interfaces.context import StreamlitContext
from src.shared_kernel.schemas.message import Message


class ContextChatPage(StreamlitContext):
    def __init__(self):
        super().__init__(state_name="chat_context")

        if not self.get_item("messages"):
            self.set_item("messages", [])

    def get_messages(self) -> list[Message]:
        return self.get_item("messages")

    def add_message(self, message: Message):
        messages = self.get_messages()
        messages.append(message)
