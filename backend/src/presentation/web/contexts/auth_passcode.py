from src.presentation.web.exceptions.user_unauthorized import UserUnauthorizedException
from src.presentation.web.interfaces.context import StreamlitContext
from src.shared_kernel.config import Config

import time
import hmac


class ContextAuthPasscode(StreamlitContext):
    def __init__(self):
        super().__init__(state_name="auth_passcode")

        if Config.Streamlit.DISABLE_PASSWORD or not Config.Streamlit.PASSWORD:
            self.disable_password = True
        else:
            self.disable_password = False

        if not self.get_item("is_user_authenticated"):
            self.set_item("is_user_authenticated", False)

    @property
    def _disable_password(self) -> bool:
        if Config.Streamlit.DISABLE_PASSWORD or not Config.Streamlit.PASSWORD:
            return True
        else:
            return False

    @property
    def _streamlit_password(self) -> str | None:
        return Config.Streamlit.PASSWORD or None

    def is_user_authenticated(self) -> bool:
        if self._disable_password:
            return True

        return self.get_item("is_user_authenticated")

    def authenticate_password(self, password: str):
        if self._disable_password:
            return True

        if password and hmac.compare_digest(password, self._streamlit_password):
            self.set_item("is_user_authenticated", True)

        else:
            time.sleep(2)
            raise UserUnauthorizedException()
