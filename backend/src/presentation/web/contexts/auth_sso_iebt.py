import base64
import json
import urllib.parse

import requests
from streamlit.runtime.state import QueryParamsProxy

from ..exceptions.cannot_decode_token import CannotDecodeToken
from ..exceptions.user_unauthorized import UserUnauthorizedException
from src.presentation.web.interfaces.context import StreamlitContext
from src.shared_kernel.config import Config

import time


class ContextAuthSsoIebt(StreamlitContext):
    def __init__(self):
        super().__init__(state_name="auth_passcode")


    def is_user_authenticated(self) -> bool:
        return self.get_item("is_user_authenticated")

    def login(self, query_params: QueryParamsProxy) -> None:
        token = self.parse_token(query_params)

        if token:
            self.set_item("is_user_authenticated", True)
            self.set_item("token", token)

        else:
            raise UserUnauthorizedException()

    def logout(self) -> None:
        self.clear_item("is_user_authenticated")
        self.clear_item("token")


    @staticmethod
    def generate_auth_url():
        """Gera a URL de autenticação para o fluxo de OAuth"""
        state = str(int(time.time()))

        auth_params = {
            "response_type": "code",
            "client_id": Config.IEBT.SSO.CLIENT_ID,
            "redirect_uri": Config.IEBT.SSO.REDIRECT_URI,
            "scope": "openid profile email",
            "state": state,
            "method": "google"
        }

        auth_endpoint = f"{Config.IEBT.SSO.API_AUTH_BASE_URL}/v1/auth/login-page"
        auth_url = f"{auth_endpoint}?{urllib.parse.urlencode(auth_params)}"

        return auth_url


    @staticmethod
    def exchange_code_for_token_post(code):
        """Troca o código de autorização por um token usando apenas o método POST"""
        token_endpoint = f"{Config.IEBT.SSO.API_AUTH_BASE_URL}/v1/auth/oauth/token"

        # Preparar credenciais de autenticação
        auth_credentials = f"{Config.IEBT.SSO.CLIENT_ID}:{Config.IEBT.SSO.CLIENT_SECRET}"
        auth_token = base64.b64encode(auth_credentials.encode()).decode()

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {auth_token}"
        }

        data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": Config.IEBT.SSO.REDIRECT_URI,
            "client_id": Config.IEBT.SSO.CLIENT_ID,
            "client_secret": Config.IEBT.SSO.CLIENT_SECRET,
        }

        try:
            response = requests.post(token_endpoint, headers=headers, data=data)

            if response.status_code != 200:
                raise UserUnauthorizedException()

            return response.json()
        except Exception as e:
            raise UserUnauthorizedException()


    @staticmethod
    def decode_token(token):
        """Decodifica o token JWT para exibir seus dados"""
        parts = token.split('.')
        if len(parts) != 3:
            return "Formato de token inválido"

        # Decodificar a parte do payload
        padded = parts[1] + "=" * (-len(parts[1]) % 4)  # Adiciona padding se necessário
        try:
            decoded = base64.b64decode(padded)
            return json.loads(decoded)
        except Exception as e:
            raise CannotDecodeToken()


    @classmethod
    def parse_token(cls, query_params: QueryParamsProxy):
        if "token" in query_params:
            token = query_params["token"]
            return cls.decode_token(token)

        elif "code" in query_params:
            code = query_params["code"]

            tokens = cls.exchange_code_for_token_post(code)
            return tokens
