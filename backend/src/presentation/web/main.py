from src.presentation.web.contexts.auth_passcode import ContextAuthPasscode
from src.presentation.web.contexts.auth_sso_iebt import ContextAuthSsoIebt
from src.presentation.web.interfaces.streamlit_view import StreamlitView
from src.presentation.web.pages.login_sso import PageLoginSsoIebt
from src.presentation.web.router import PresentationWebStreamlitRouter

import streamlit as st


class PresentationWebStreamlitMain(StreamlitView):
    def render(self):
        auth_context = ContextAuthSsoIebt()

        if not auth_context.is_user_authenticated():
            PageLoginSsoIebt().render()

        else:
            st.logo("assets/logo_cognit.png", size="large")
            PresentationWebStreamlitRouter().render()
