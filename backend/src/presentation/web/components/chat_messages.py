from src.presentation.web.interfaces.streamlit_view import StreamlitView
from src.shared_kernel.schemas.message import Message
import streamlit as st


class ComponentChatMessages(StreamlitView):
    def __init__(self, messages: list[Message]):
        super().__init__()
        self.messages = messages

    def render(self):
        for msg in self.messages:
            st.chat_message(msg.author).write(msg.content)
