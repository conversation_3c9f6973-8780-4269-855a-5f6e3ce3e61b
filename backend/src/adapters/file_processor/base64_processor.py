"""
Base64 File Processor Implementation
"""

import base64
from typing import Dict, Any

from .interface import FileProcessorInterface
from src.shared_kernel.schemas.temp_file import TempFile


class Base64FileProcessor(FileProcessorInterface):
    """Implementation of file processor for base64 conversion"""
    
    # Supported MIME types for GPT-4o
    SUPPORTED_TYPES = {
        'application/pdf',
        'image/jpeg',
        'image/png', 
        'image/gif',
        'image/webp',
        'text/plain',
        'text/markdown',
        'application/json',
        'text/csv'
    }
    
    async def convert_to_base64(self, file_content: bytes, temp_file: TempFile) -> str:
        """Convert file content to base64"""
        try:
            return base64.b64encode(file_content).decode('utf-8')
        except Exception as e:
            raise ValueError(f"Error converting file to base64: {e}")
    
    def create_llm_message_content(self, base64_content: str, temp_file: TempFile, user_text: str, provider: str = "openai") -> Dict[str, Any]:

        if temp_file.mime_type.startswith('image/'):
            # Para imagens, usar formato image_url (funciona em ambos)
            content = [
                {
                    "type": "text",
                    "text": user_text
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{temp_file.mime_type};base64,{base64_content}"
                    }
                }
            ]
        else:
            # Para PDFs e outros documentos
            if provider.lower() == "anthropic":
                # Anthropic/Claude suporta formato file
                if temp_file.mime_type == 'application/pdf':
                    content = [
                        {
                            "type": "document",
                            "source": {
                                "type": "base64",
                                "media_type": "application/pdf",
                                "data": base64_content
                            }
                        },
                        {
                            "type": "text",
                            "text": user_text
                        }
                    ]
                elif temp_file.mime_type.startswith("text/"):
                    # Arquivos de texto plano => converter para string e embutir
                    decoded_text = base64.b64decode(base64_content).decode("utf-8")
                    content = [
                        {
                            "type": "text",
                            "text": f"{user_text}\n\nConteúdo do arquivo ({temp_file.original_filename}):\n\n{decoded_text}"
                        }
                    ]

                else:
                    # Fallback para arquivos não suportados
                    content = [
                        {
                            "type": "text",
                            "text": f"{user_text}\n\n[Arquivo {temp_file.original_filename} com MIME {temp_file.mime_type} não é suportado diretamente.]"
                        }
                    ]
            else:
                # OpenAI/GPT-4o - tentar formato file primeiro, fallback para texto
                # Alguns modelos OpenAI podem suportar o formato file
                try:
                    content = [
                        {
                            "type": "text",
                            "text": user_text
                        },
                        {
                            "type": "file",
                            "source_type": "base64",
                            "data": base64_content,
                            "mime_type": temp_file.mime_type,
                            "filename": temp_file.original_filename
                        }
                    ]
                except:
                    # Fallback: incluir como texto com base64 truncado
                    content = [
                        {
                            "type": "text",
                            "text": f"{user_text}\n\n[Documento anexado: {temp_file.original_filename}]\n[Tipo: {temp_file.mime_type}]\n[Tamanho: {len(base64_content)} caracteres base64]\n\nPor favor, analise este documento baseado nas informações fornecidas."
                        }
                    ]

        return content
    
    def is_supported_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported"""
        return mime_type in self.SUPPORTED_TYPES
    
    def get_supported_types(self) -> set:
        """Get all supported MIME types"""
        return self.SUPPORTED_TYPES.copy()
    
    def validate_file_for_llm(self, temp_file: TempFile) -> bool:
        """
        Validate if file is suitable for LLM processing
        
        Args:
            temp_file: TempFile domain model
            
        Returns:
            True if valid, False otherwise
        """
        # Check if type is supported
        if not self.is_supported_type(temp_file.mime_type):
            return False
        
        # Check if file is not expired
        if temp_file.is_expired():
            return False
        
        # Check file size (GPT-4o has limits)
        max_size = 20 * 1024 * 1024  # 20MB limit
        if temp_file.file_size > max_size:
            return False
        
        return True
    
    def get_file_type_description(self, mime_type: str) -> str:
        """Get human-readable description of file type"""
        descriptions = {
            'application/pdf': 'PDF Document',
            'image/jpeg': 'JPEG Image',
            'image/png': 'PNG Image',
            'image/gif': 'GIF Image', 
            'image/webp': 'WebP Image',
            'text/plain': 'Text File',
            'text/markdown': 'Markdown File',
            'application/json': 'JSON File',
            'text/csv': 'CSV File'
        }
        
        return descriptions.get(mime_type, 'Unknown File Type')
