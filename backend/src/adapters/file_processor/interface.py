"""
File Processor Interface
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from src.shared_kernel.schemas.temp_file import TempFile


class FileProcessorInterface(ABC):
    """Interface for file processing operations"""
    
    @abstractmethod
    async def convert_to_base64(self, file_content: bytes, temp_file: TempFile) -> str:
        """
        Convert file content to base64 string
        
        Args:
            file_content: Binary content of the file
            temp_file: TempFile domain model with metadata
            
        Returns:
            Base64 encoded string
        """
        pass
    
    @abstractmethod
    def create_llm_message_content(self, base64_content: str, temp_file: TempFile, user_text: str, provider: str = "openai") -> Dict[str, Any]:
        """
        Create LLM message content in the format expected by the provider

        Args:
            base64_content: Base64 encoded file content
            temp_file: TempFile domain model with metadata
            user_text: User's text message
            provider: LLM provider ("openai" or "anthropic")

        Returns:
            Dictionary with message content for LLM
        """
        pass
    
    @abstractmethod
    def is_supported_type(self, mime_type: str) -> bool:
        """
        Check if file type is supported for LLM processing
        
        Args:
            mime_type: MIME type of the file
            
        Returns:
            True if supported, False otherwise
        """
        pass
