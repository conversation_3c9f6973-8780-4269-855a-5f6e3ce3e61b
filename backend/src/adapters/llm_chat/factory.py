from typing import Optional
from .interface import LlmChatAdapterInterface
from .provider.openai import LlmChatAdapterOpenAI
from .provider.anthropic import LlmChatAdapterAnthropic


class LlmChatAdapterFactory:
    """Factory para criar adapters de LLM baseado no provider"""
    
    @staticmethod
    def create_adapter(provider: str, model: str) -> Optional[LlmChatAdapterInterface]:
        """
        Cria um adapter de LLM baseado no provider
        
        Args:
            provider: Nome do provider ('openai', 'anthropic', etc.)
            model: Nome do modelo
            
        Returns:
            Adapter correspondente ou None se provider não suportado
        """
        provider = provider.lower()
        
        if provider == 'openai':
            return LlmChatAdapterOpenAI(model=model)
        elif provider == 'anthropic':
            return LlmChatAdapterAnthropic(model=model)
        else:
            return None
    
    @staticmethod
    def get_supported_providers() -> list[str]:
        """Retorna lista de providers suportados"""
        return ['openai', 'anthropic']
    
    @staticmethod
    def is_provider_supported(provider: str) -> bool:
        """Verifica se um provider é suportado"""
        return provider.lower() in LlmChatAdapterFactory.get_supported_providers()
