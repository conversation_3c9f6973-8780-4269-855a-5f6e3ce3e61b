from langchain_core.language_models import BaseChatModel
from langchain_openai import ChatOpenAI
from ..interface import LlmChatAdapterInterface

from src.shared_kernel.config import Config


class LlmChatAdapterOpenAI(LlmChatAdapterInterface):
    def __init__(
            self,
            model: str = "gpt-4o-mini",
    ) -> None:
        self.model = model
        self._client = ChatOpenAI(
            temperature=0.7,
            api_key=Config.OpenAI.API_KEY,
            model=model,
        )

    @property
    def client(self) -> BaseChatModel:
        return self._client
