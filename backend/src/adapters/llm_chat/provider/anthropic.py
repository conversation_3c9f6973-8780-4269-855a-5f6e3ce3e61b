from langchain_core.language_models import BaseChatModel
from langchain_anthropic import ChatAnthropic
from ..interface import LlmChatAdapterInterface

from src.shared_kernel.config import Config


class LlmChatAdapterAnthropic(LlmChatAdapterInterface):
    def __init__(
            self,
            model: str = "claude-3-5-sonnet-20241022",
    ) -> None:
        self.model = model
        self._client = ChatAnthropic(
            temperature=0.7,
            api_key=Config.Anthropic.API_KEY,
            model_name=model,
            timeout=1000,
            stop=None,
        )

    @property
    def client(self) -> BaseChatModel:
        return self._client
