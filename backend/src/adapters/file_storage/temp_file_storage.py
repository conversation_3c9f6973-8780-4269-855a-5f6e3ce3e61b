"""
Temporary File Storage Implementation
"""

import os
import json
import aiofiles
from typing import Optional, List
from datetime import datetime

from .interface import FileStorageInterface
from src.shared_kernel.schemas.temp_file import TempFile


class TempFileStorageAdapter(FileStorageInterface):
    """Implementation of file storage using temporary directory"""
    
    def __init__(self, base_dir: str = "/tmp/cognit_files"):
        self.base_dir = base_dir
        self.metadata_dir = os.path.join(base_dir, "metadata")
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure storage directories exist"""
        os.makedirs(self.base_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
    
    def _get_file_path(self, file_id: str) -> str:
        """Get full path for file content"""
        return os.path.join(self.base_dir, file_id)
    
    def _get_metadata_path(self, file_id: str) -> str:
        """Get full path for file metadata"""
        return os.path.join(self.metadata_dir, f"{file_id}.json")
    
    async def save_file(self, file_content: bytes, temp_file: TempFile) -> bool:
        """Save file content and metadata"""
        try:
            # Save file content
            file_path = self._get_file_path(temp_file.file_id)
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # Save metadata
            metadata_path = self._get_metadata_path(temp_file.file_id)
            metadata = {
                'file_id': temp_file.file_id,
                'original_filename': temp_file.original_filename,
                'stored_filename': temp_file.stored_filename,
                'mime_type': temp_file.mime_type,
                'file_size': temp_file.file_size,
                'upload_timestamp': temp_file.upload_timestamp.isoformat(),
                'expiry_timestamp': temp_file.expiry_timestamp.isoformat(),
                'file_path': file_path
            }
            
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(json.dumps(metadata, indent=2))
            
            return True
            
        except Exception as e:
            return False
    
    async def get_file(self, file_id: str) -> Optional[bytes]:
        """Retrieve file content"""
        try:
            file_path = self._get_file_path(file_id)
            if not os.path.exists(file_path):
                return None
            
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
                
        except Exception as e:
            return None
    
    async def get_file_info(self, file_id: str) -> Optional[TempFile]:
        """Get file metadata"""
        try:
            metadata_path = self._get_metadata_path(file_id)
            if not os.path.exists(metadata_path):
                return None
            
            async with aiofiles.open(metadata_path, 'r') as f:
                metadata = json.loads(await f.read())
            
            return TempFile(
                file_id=metadata['file_id'],
                original_filename=metadata['original_filename'],
                stored_filename=metadata['stored_filename'],
                mime_type=metadata['mime_type'],
                file_size=metadata['file_size'],
                upload_timestamp=datetime.fromisoformat(metadata['upload_timestamp']),
                expiry_timestamp=datetime.fromisoformat(metadata['expiry_timestamp']),
                file_path=metadata['file_path']
            )
            
        except Exception as e:
            return None
    
    async def delete_file(self, file_id: str) -> bool:
        """Delete file and metadata"""
        try:
            file_path = self._get_file_path(file_id)
            metadata_path = self._get_metadata_path(file_id)
            
            # Delete file content
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Delete metadata
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return True
            
        except Exception as e:
            return False
    
    async def file_exists(self, file_id: str) -> bool:
        """Check if file exists"""
        file_path = self._get_file_path(file_id)
        metadata_path = self._get_metadata_path(file_id)
        return os.path.exists(file_path) and os.path.exists(metadata_path)
    
    async def cleanup_expired_files(self) -> int:
        """Remove expired files"""
        cleaned_count = 0
        
        try:
            # List all metadata files
            if not os.path.exists(self.metadata_dir):
                return 0
            
            for filename in os.listdir(self.metadata_dir):
                if filename.endswith('.json'):
                    file_id = filename[:-5]  # Remove .json extension
                    
                    file_info = await self.get_file_info(file_id)
                    if file_info and file_info.is_expired():
                        if await self.delete_file(file_id):
                            cleaned_count += 1
            
            return cleaned_count
            
        except Exception as e:
            return cleaned_count
    
    async def list_files(self) -> List[TempFile]:
        """List all files"""
        files = []
        
        try:
            if not os.path.exists(self.metadata_dir):
                return files
            
            for filename in os.listdir(self.metadata_dir):
                if filename.endswith('.json'):
                    file_id = filename[:-5]  # Remove .json extension
                    file_info = await self.get_file_info(file_id)
                    if file_info:
                        files.append(file_info)
            
            return files
            
        except Exception as e:
            return files
