"""
File Storage Adapter Interface
"""

from abc import ABC, abstractmethod
from typing import Optional, List
from src.shared_kernel.schemas.temp_file import TempFile


class FileStorageInterface(ABC):
    """Interface for file storage operations"""
    
    @abstractmethod
    async def save_file(self, file_content: bytes, temp_file: TempFile) -> bool:
        """
        Save file content to storage
        
        Args:
            file_content: Binary content of the file
            temp_file: TempFile domain model with metadata
            
        Returns:
            True if saved successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_file(self, file_id: str) -> Optional[bytes]:
        """
        Retrieve file content by ID
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            File content as bytes, or None if not found
        """
        pass
    
    @abstractmethod
    async def get_file_info(self, file_id: str) -> Optional[TempFile]:
        """
        Get file metadata by ID
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            TempFile domain model, or None if not found
        """
        pass
    
    @abstractmethod
    async def delete_file(self, file_id: str) -> bool:
        """
        Delete file from storage
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def file_exists(self, file_id: str) -> bool:
        """
        Check if file exists in storage
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            True if file exists, False otherwise
        """
        pass
    
    @abstractmethod
    async def cleanup_expired_files(self) -> int:
        """
        Remove expired files from storage
        
        Returns:
            Number of files cleaned up
        """
        pass
    
    @abstractmethod
    async def list_files(self) -> List[TempFile]:
        """
        List all files in storage
        
        Returns:
            List of TempFile domain models
        """
        pass
