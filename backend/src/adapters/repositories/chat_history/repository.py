from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime
import math

from .interface import ChatSessionRepositoryInterface, ChatMessageRepositoryInterface
from .models.chat_session import ChatSession
from .models.chat_message import ChatMessage, Author


class ChatSessionRepository(ChatSessionRepositoryInterface):
    """ChatSession repository implementation using SQLAlchemy"""
    
    def __init__(self, session: Session):
        self._session = session
    
    def create(self, user_id: int, title: Optional[str] = None) -> ChatSession:
        """Create a new chat session"""
        chat_session = ChatSession(
            user_id=user_id,
            title=title
        )
        self._session.add(chat_session)
        self._session.commit()
        self._session.refresh(chat_session)
        return chat_session
    
    def find_by_id_and_user(self, session_id: int, user_id: int) -> Optional[ChatSession]:
        """Find chat session by ID and user ID"""
        return self._session.query(ChatSession).filter(
            ChatSession.id == session_id,
            ChatSession.user_id == user_id,
            ChatSession.deleted_at.is_(None)
        ).first()
    
    def find_by_user_paginated(self, user_id: int, page: int, per_page: int) -> Tuple[List[ChatSession], int]:
        """Find chat sessions by user with pagination"""
        query = self._session.query(ChatSession).filter(
            ChatSession.user_id == user_id,
            ChatSession.deleted_at.is_(None)
        ).order_by(ChatSession.last_interaction.desc())
        
        total = query.count()
        sessions = query.offset((page - 1) * per_page).limit(per_page).all()
        
        return sessions, total
    
    def update_title(self, session: ChatSession, title: str) -> ChatSession:
        """Update session title"""
        session.title = title
        self._session.commit()
        self._session.refresh(session)
        return session
    
    def soft_delete(self, session: ChatSession) -> ChatSession:
        """Soft delete a session"""
        session.deleted_at = datetime.utcnow()
        self._session.commit()
        self._session.refresh(session)
        return session

    def update_last_interaction(self, session_id: int, user_id: int) -> bool:
        """Update last_interaction timestamp for a session"""
        try:
            from sqlalchemy import func

            result = self._session.query(ChatSession).filter(
                ChatSession.id == session_id,
                ChatSession.user_id == user_id,
                ChatSession.deleted_at.is_(None)
            ).update({
                ChatSession.last_interaction: func.now()
            })

            if result > 0:
                self._session.commit()
                return True
            else:
                return False

        except Exception:
            self._session.rollback()
            return False


class ChatMessageRepository(ChatMessageRepositoryInterface):
    """ChatMessage repository implementation using SQLAlchemy"""
    
    def __init__(self, session: Session):
        self._session = session
    
    def create(
        self,
        session_id: int,
        author: str,
        content: str,
        model_id: Optional[int] = None,
        attached_files: Optional[List[Dict[str, str]]] = None
    ) -> ChatMessage:
        """Create a new chat message"""
        message = ChatMessage(
            session_id=session_id,
            author=Author(author),
            content=content,
            model_id=model_id,
            attached_files=attached_files
        )
        self._session.add(message)
        self._session.commit()
        self._session.refresh(message)
        return message
    
    def find_by_session(self, session_id: int) -> List[ChatMessage]:
        """Find all messages for a session"""
        return self._session.query(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).order_by(ChatMessage.timestamp.asc()).all()
    
    def count_by_session(self, session_id: int) -> int:
        """Count messages in a session"""
        return self._session.query(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).count()
