from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.sql import func

from src.infrastructure.database.base import Base
from src.adapters.repositories.user.models.user import User


class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey(User.id), nullable=False)

    title = Column(String, nullable=True, default=None)
    first_interaction = Column(DateTime, default=func.now())
    last_interaction = Column(DateTime, default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime, nullable=True, default=None)

    def soft_delete(self):
        """Marca a sessão como deletada (soft delete)"""
        self.deleted_at = func.now()

    def is_deleted(self) -> bool:
        """Verifica se a sessão foi deletada"""
        return self.deleted_at is not None

    @classmethod
    def active_sessions(cls):
        """Retorna query para sessões ativas (não deletadas)"""
        return cls.query.filter(cls.deleted_at.is_(None))
