from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, <PERSON>, Enum, DateTime, JSON
from sqlalchemy.sql import func
import enum

from src.infrastructure.database.base import Base
from .chat_session import ChatSession
# Import LLMModel for foreign key reference
from src.adapters.repositories.llm_model.models.llm_model import LLMModel


class Author(enum.Enum):
    BOT = 'bot'
    USER = 'user'


class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Integer, ForeignKey("chat_sessions.id"))
    author = Column(Enum(Author))
    content = Column(String)
    model_id = Column(Integer, ForeignKey(LLMModel.id), nullable=True)
    timestamp = Column(DateTime, default=func.now())

    # File attachments (array of files)
    attached_files = Column(JSON, nullable=True)  # [{"name": "file.pdf", "mime_type": "application/pdf"}]
