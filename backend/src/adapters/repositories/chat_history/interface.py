from abc import ABC, abstractmethod
from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime

from .models.chat_session import ChatSession
from .models.chat_message import ChatMessage


class ChatSessionRepositoryInterface(ABC):
    """Interface for ChatSession repository operations"""
    
    @abstractmethod
    def create(self, user_id: int, title: Optional[str] = None) -> ChatSession:
        """Create a new chat session"""
        pass
    
    @abstractmethod
    def find_by_id_and_user(self, session_id: int, user_id: int) -> Optional[ChatSession]:
        """Find chat session by ID and user ID"""
        pass
    
    @abstractmethod
    def find_by_user_paginated(self, user_id: int, page: int, per_page: int) -> Tuple[List[ChatSession], int]:
        """Find chat sessions by user with pagination"""
        pass
    
    @abstractmethod
    def update_title(self, session: ChatSession, title: str) -> ChatSession:
        """Update session title"""
        pass
    
    @abstractmethod
    def soft_delete(self, session: ChatSession) -> ChatSession:
        """Soft delete a session"""
        pass

    @abstractmethod
    def update_last_interaction(self, session_id: int, user_id: int) -> bool:
        """Update last_interaction timestamp for a session"""
        pass


class ChatMessageRepositoryInterface(ABC):
    """Interface for ChatMessage repository operations"""
    
    @abstractmethod
    def create(
        self,
        session_id: int,
        author: str,
        content: str,
        model_id: Optional[int] = None,
        attached_files: Optional[List[Dict[str, str]]] = None
    ) -> ChatMessage:
        """
        Create a new chat message

        Args:
            attached_files: List of file info dicts like [{"name": "file.pdf", "mime_type": "application/pdf"}]
        """
        pass
    
    @abstractmethod
    def find_by_session(self, session_id: int) -> List[ChatMessage]:
        """Find all messages for a session"""
        pass
    
    @abstractmethod
    def count_by_session(self, session_id: int) -> int:
        """Count messages in a session"""
        pass
