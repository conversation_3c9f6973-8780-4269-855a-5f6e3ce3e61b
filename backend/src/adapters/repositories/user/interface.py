from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from .models.user import User


class UserRepositoryInterface(ABC):
    """Interface for User repository operations"""
    
    @abstractmethod
    def find_by_google_sub(self, google_sub: str) -> Optional[User]:
        """Find user by Google sub ID"""
        pass
    
    @abstractmethod
    def create(self, user_data: Dict[str, Any]) -> User:
        """Create a new user"""
        pass
    
    @abstractmethod
    def update(self, user: User, user_data: Dict[str, Any]) -> User:
        """Update an existing user"""
        pass
    
    @abstractmethod
    def find_by_id(self, user_id: int) -> Optional[User]:
        """Find user by ID"""
        pass
