from sqlalchemy.orm import Session
from typing import Optional, Dict, Any

from .interface import UserRepositoryInterface
from .models.user import User


class UserRepository(UserRepositoryInterface):
    """User repository implementation using SQLAlchemy"""
    
    def __init__(self, session: Session):
        self._session = session
    
    def find_by_google_sub(self, google_sub: str) -> Optional[User]:
        """Find user by Google sub ID"""
        return self._session.query(User).filter(User.google_sub == google_sub).first()
    
    def create(self, user_data: Dict[str, Any]) -> User:
        """Create a new user"""
        user = User(
            google_sub=user_data.get("sub"),
            email=user_data.get("email"),
            name=user_data.get("name", "")
        )
        self._session.add(user)
        self._session.commit()
        self._session.refresh(user)
        return user
    
    def update(self, user: User, user_data: Dict[str, Any]) -> User:
        """Update an existing user"""
        for key, value in user_data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        self._session.commit()
        self._session.refresh(user)
        return user
    
    def find_by_id(self, user_id: int) -> Optional[User]:
        """Find user by ID"""
        return self._session.query(User).filter(User.id == user_id).first()
