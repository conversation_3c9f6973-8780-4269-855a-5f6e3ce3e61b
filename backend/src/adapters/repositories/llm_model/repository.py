from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import List, Optional

from .interface import LLMModelRepositoryInterface
from .models.llm_model import LLMModel


class LLMModelRepository(LLMModelRepositoryInterface):
    """LLMModel repository implementation using SQLAlchemy"""
    
    def __init__(self, session: Session):
        self._session = session
    
    def get_all(self, active_only: bool = True) -> List[LLMModel]:
        """Get all LLM models, optionally filtering by active status"""
        query = select(LLMModel)
        
        if active_only:
            query = query.where(LLMModel.is_active == True)
        
        result = self._session.execute(query)
        return result.scalars().all()
    
    def get_by_id(self, model_id: int) -> Optional[LLMModel]:
        """Get LLM model by ID"""
        query = select(LLMModel).where(LLMModel.id == model_id)
        result = self._session.execute(query)
        return result.scalar_one_or_none()
    
    def get_by_name(self, name: str) -> Optional[LLMModel]:
        """Get LLM model by name"""
        query = select(LLMModel).where(LLMModel.name == name)
        result = self._session.execute(query)
        return result.scalar_one_or_none()
    
    def create(self, model_data: dict) -> LLMModel:
        """Create a new LLM model"""
        model = LLMModel(**model_data)
        self._session.add(model)
        self._session.commit()
        self._session.refresh(model)
        return model
    
    def update(self, model: LLMModel, model_data: dict) -> LLMModel:
        """Update an existing LLM model"""
        for key, value in model_data.items():
            if hasattr(model, key):
                setattr(model, key, value)
        
        self._session.commit()
        self._session.refresh(model)
        return model
