from abc import ABC, abstractmethod
from typing import List, Optional
from .models.llm_model import LLMModel


class LLMModelRepositoryInterface(ABC):
    """Interface for LLMModel repository operations"""
    
    @abstractmethod
    def get_all(self, active_only: bool = True) -> List[LLMModel]:
        """Get all LLM models, optionally filtering by active status"""
        pass
    
    @abstractmethod
    def get_by_id(self, model_id: int) -> Optional[LLMModel]:
        """Get LLM model by ID"""
        pass
    
    @abstractmethod
    def get_by_name(self, name: str) -> Optional[LLMModel]:
        """Get LLM model by name"""
        pass
    
    @abstractmethod
    def create(self, model_data: dict) -> LLMModel:
        """Create a new LLM model"""
        pass
    
    @abstractmethod
    def update(self, model: LLMModel, model_data: dict) -> LLMModel:
        """Update an existing LLM model"""
        pass
