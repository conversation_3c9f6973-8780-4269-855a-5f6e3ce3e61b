from sqlalchemy import Column, Integer, String, Boolean, DateTime, Numeric, func
from sqlalchemy.orm import relationship
from src.infrastructure.database.base import Base


class LLMModel(Base):
    __tablename__ = "llm_models"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    provider = Column(String(100), nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)
    base_url = Column(String(255), nullable=True)
    max_tokens = Column(Integer, nullable=True)
    context_window = Column(Integer, nullable=True)
    price_type = Column(String(50), nullable=True)
    price_usd = Column(Numeric(10, 6), nullable=True)
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)
