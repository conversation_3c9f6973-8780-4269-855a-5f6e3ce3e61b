import httpx
import base64

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from typing import Dict, Any
from google.oauth2 import id_token
from google.auth.transport import requests
from src.shared_kernel.config import Config
from ..interface import AuthAdapterInterface

class GoogleAuthAdapter(AuthAdapterInterface):
    
    def __init__(self):
        self.client_id = Config.IEBT.SSO.CLIENT_ID
        self.client_secret = Config.IEBT.SSO.CLIENT_SECRET
        self.redirect_uri = Config.IEBT.SSO.REDIRECT_URI
        # URLs oficiais do Google OAuth
        self.auth_base_url = "https://accounts.google.com/o/oauth2/v2/"
        self.exchange_token_url = "https://oauth2.googleapis.com/token"

    async def get_authorization_url(self) -> str:
        authorization_url = (
            f"{self.auth_base_url}auth?"
            f"client_id={self.client_id}&"
            f"redirect_uri={self.redirect_uri}&"
            "scope=openid email profile&"
            "response_type=code&"
            "access_type=offline&"
            "prompt=consent"
        )
        
        return authorization_url

    async def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri,
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(self.exchange_token_url, data=data, headers=headers)

            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Google OAuth Error: {response.text}"
                )
            print('toke para teste:',response.json())
            return response.json()

    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
        
        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(user_info_url, headers=headers)
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Falha ao obter informações do usuário"
                )
            
            return response.json()

    async def verify_id_token(self, id_token_str: str) -> Dict[str, Any]:
        try:
            request = requests.Request()
            
            payload = id_token.verify_oauth2_token(
                id_token_str, 
                request, 
                self.client_id
            )
            
            if payload.get("iss") not in ["accounts.google.com", "https://accounts.google.com"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Token não é do Google"
                )
            
            return payload
            
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Token ID inválido: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Erro ao verificar token: {str(e)}"
            )



