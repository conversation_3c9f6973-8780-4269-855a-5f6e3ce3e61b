from abc import ABC, abstractmethod
from typing import Dict, Any


class AuthAdapterInterface(ABC):
    
    @abstractmethod
    async def get_authorization_url(self) -> str:
        pass
    
    @abstractmethod
    async def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    async def verify_id_token(self, id_token: str) -> Dict[str, Any]:
        pass
