from sqlalchemy.orm import Session
from decimal import Decimal
from src.adapters.repositories.llm_model.models.llm_model import LLMModel
from src.infrastructure.database.session import get_db_session


def seed_llm_models(db: Session) -> None:
    
    existing_count = db.query(LLMModel).count()
    if existing_count > 0:
        return
    
    llm_models_data = [
        {
            "name": "gpt-4o",
            "provider": "openai",
            "is_active": True,
            "base_url": "https://api.openai.com/v1",
            "max_tokens": 4096,
            "context_window": 128_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.015")
        },
        {
            "name": "gpt-4o-mini",
            "provider": "openai",
            "is_active": False,
            "base_url": "https://api.openai.com/v1",
            "max_tokens": 16384,
            "context_window": 128_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.0015")
        },
        {
            "name": "gpt-3.5-turbo",
            "provider": "openai",
            "is_active": False,
            "base_url": "https://api.openai.com/v1",
            "max_tokens": 4096,
            "context_window": 16_385,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.002")
        },
        {
            "name": "claude-3-5-sonnet-20241022",
            "provider": "anthropic",
            "is_active": False,
            "base_url": "https://api.anthropic.com/v1",
            "max_tokens": 8192,
            "context_window": 200_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.015")
        },
        {
            "name": "claude-3-haiku-20240307",
            "provider": "anthropic",
            "is_active": False,
            "base_url": "https://api.anthropic.com/v1",
            "max_tokens": 4096,
            "context_window": 200_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.00125")
        },
        {
            "name": "gemini-1.5-pro",
            "provider": "google",
            "is_active": False,
            "base_url": "https://generativelanguage.googleapis.com/v1",
            "max_tokens": 8192,
            "context_window": 2_000_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.0075")
        },
        {
            "name": "gemini-1.5-flash",
            "provider": "google",
            "is_active": False,
            "base_url": "https://generativelanguage.googleapis.com/v1",
            "max_tokens": 8192,
            "context_window": 1_000_000,
            "price_type": "per_1k_tokens_output",
            "price_usd": Decimal("0.0003")
        }
    ]
    
    for model_data in llm_models_data:
        llm_model = LLMModel(**model_data)
        db.add(llm_model)
    
    db.commit()


def run_seeder():
    """Run seeder with proper database session management"""
    from contextlib import closing

    with closing(get_db_session()) as gen:
        db = next(gen)
        seed_llm_models(db)


if __name__ == "__main__":
    run_seeder()
