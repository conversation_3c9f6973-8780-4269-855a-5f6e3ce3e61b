import sys
import os
from pathlib import Path

current_file = Path(__file__).resolve()
project_root = current_file.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.database.seeders.llm_models_seeder import run_seeder as run_llm_models_seeder


def run_all_seeders():

    seeders = [
        run_llm_models_seeder,
    ]
    
    for seeder_function in seeders:
        try:
            seeder_function()
        except Exception as e:
            print(f"Erro ao executar seeder: {str(e)}")


if __name__ == "__main__":
    run_all_seeders()
