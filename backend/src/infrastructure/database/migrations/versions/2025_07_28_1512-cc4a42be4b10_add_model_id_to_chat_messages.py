"""add_model_id_to_chat_messages

Revision ID: cc4a42be4b10
Revises: 1d4e1f1d0096
Create Date: 2025-07-28 15:12:02.458713

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cc4a42be4b10'
down_revision: Union[str, Sequence[str], None] = '1d4e1f1d0096'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_messages', sa.Column('model_id', sa.Integer(), nullable=True))
    op.add_column('chat_messages', sa.Column('attached_files', sa.JSON(), nullable=True))
    op.create_foreign_key(None, 'chat_messages', 'llm_models', ['model_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'chat_messages', type_='foreignkey')
    op.drop_column('chat_messages', 'attached_files')
    op.drop_column('chat_messages', 'model_id')
    # ### end Alembic commands ###
