"""create_llm_models_table

Revision ID: 1d4e1f1d0096
Revises: 11a6176b4fff
Create Date: 2025-07-22 16:29:29.839824

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d4e1f1d0096'
down_revision: Union[str, Sequence[str], None] = '11a6176b4fff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        'llm_models',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('provider', sa.String(length=100), nullable=False),
        sa.Column('is_active', sa.<PERSON>, nullable=False, default=True),
        sa.Column('base_url', sa.String(length=255), nullable=True),
        sa.Column('max_tokens', sa.Integer, nullable=True),
        sa.Column('context_window', sa.Integer, nullable=True),
        sa.Column('price_type', sa.String(length=50), nullable=True),
        sa.Column('price_usd', sa.Numeric(10, 6), nullable=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )   
    pass


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table('llm_models')
