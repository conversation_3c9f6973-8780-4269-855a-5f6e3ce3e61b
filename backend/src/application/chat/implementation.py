import asyncio
from typing import Dict, Any, Callable

from .interface import ChatApplicationInterface
from src.shared_kernel.schemas.message import Message
from src.presentation.api.schemas.chat import ModelValidationSchema
from src.adapters.llm_chat.factory import LlmChatAdapterFactory


class ChatApplication(ChatApplicationInterface):

    def stream(self, messages: list[Message], **kwargs):
        messages_as_tuples = self.convert_messages_to_langchain_format(messages)
        client = self.llm_provider.client
        return client.stream(input=messages_as_tuples, **kwargs)

    async def astream(self, messages: list[Message], **kwargs):
        """Método assíncrono para streaming"""
        messages_as_tuples = self.convert_messages_to_langchain_format(messages)
        client = self.llm_provider.client
        async for chunk in client.astream(input=messages_as_tuples, **kwargs):
            yield chunk

    async def process_websocket_chat(
        self,
        chat_request: Any,
        model_service: Any,
        send_message_callback: Callable[[Dict[str, Any]], None],
        is_stopped_callback: Callable[[], bool],
        log_chat_service: Any = None,
        session_id: int = None
    ) -> None:
        """
        Processa uma requisição de chat via WebSocket com streaming controlado
        """
        try:
            # Buscar e validar modelo
            model = await model_service.get_model_by_id(chat_request.model_id)
            if not model:
                await send_message_callback({
                    "type": "error",
                    "message": "Modelo LLM não encontrado"
                })
                return

            # Validar modelo
            model_validation = ModelValidationSchema(
                model_id=model.id,
                is_active=model.is_active,
                provider=model.provider,
                name=model.name
            )

            if not model_validation.is_active:
                await send_message_callback({
                    "type": "error",
                    "message": "Modelo LLM não está ativo"
                })
                return

            if not model_validation.is_supported():
                await send_message_callback({
                    "type": "error",
                    "message": f"Provider '{model_validation.provider}' não suportado"
                })
                return

            # Buscar contexto das últimas mensagens se disponível
            conversation_history = []
            if log_chat_service and session_id:
                conversation_history = log_chat_service.get_recent_messages(session_id, limit=9)

            # Converter para mensagens de domínio incluindo contexto
            messages = chat_request.to_domain_messages(conversation_history=conversation_history)

            # Configurar provedor LLM usando factory
            llm_provider = LlmChatAdapterFactory.create_adapter(
                provider=model_validation.provider,
                model=model_validation.name
            )

            if not llm_provider:
                await send_message_callback({
                    "type": "error",
                    "message": f"Falha ao criar adapter para provider '{model_validation.provider}'"
                })
                return

            chat_app = ChatApplication(llm_provider=llm_provider)

            # Enviar confirmação de início
            await send_message_callback({
                "type": "stream_start",
                "model": model_validation.name,
                "provider": model_validation.provider
            })

            # Processar streaming
            await self._process_streaming(
                chat_app,
                messages,
                model_validation,
                send_message_callback,
                is_stopped_callback
            )

        except Exception as e:
            await send_message_callback({
                "type": "error",
                "message": f"Erro ao processar chat: {str(e)}"
            })

    async def _process_streaming(
        self,
        chat_app: 'ChatApplication',
        messages: list[Message],
        model_validation: ModelValidationSchema,
        send_message_callback: Callable[[Dict[str, Any]], None],
        is_stopped_callback: Callable[[], bool]
    ) -> None:
        """
        Processa o streaming de resposta com controle de parada
        """
        async def streaming_task():
            """Task interna para o streaming que pode ser cancelada"""
            response_content = ""
            try:
                async for chunk in chat_app.astream(messages):
                    if is_stopped_callback():
                        await send_message_callback({
                            "type": "stream_interrupted",
                            "message": "Streaming interrompido pelo usuário",
                            "partial_response": response_content
                        })
                        return "interrupted"

                    if hasattr(chunk, 'content') and chunk.content:
                        response_content += chunk.content

                        if is_stopped_callback():
                            await send_message_callback({
                                "type": "stream_interrupted",
                                "message": "Streaming interrompido pelo usuário",
                                "partial_response": response_content
                            })
                            return "interrupted"

                        await send_message_callback({
                            "type": "stream_chunk",
                            "chunk": chunk.content,
                            "partial_response": response_content
                        })

                        if is_stopped_callback():
                            await send_message_callback({
                                "type": "stream_interrupted",
                                "message": "Streaming interrompido pelo usuário",
                                "partial_response": response_content
                            })
                            return "interrupted"

                # Streaming completou normalmente
                await send_message_callback({
                    "type": "stream_complete",
                    "final_response": response_content,
                    "model": model_validation.name,
                    "provider": model_validation.provider
                })
                return "completed"
            except asyncio.CancelledError:
                # Task foi cancelada externamente
                await send_message_callback({
                    "type": "stream_cancelled",
                    "message": "Streaming cancelado"
                })
                raise

        try:
            # Executar streaming com verificação de parada a cada chunk
            await streaming_task()

        except asyncio.CancelledError:
            # Task principal foi cancelada pelo comando de parada
            await send_message_callback({
                "type": "stream_interrupted",
                "message": "Streaming interrompido pelo usuário"
            })
            raise  # Re-raise para que a task seja realmente cancelada
        except Exception as e:
            await send_message_callback({
                "type": "error",
                "message": f"Erro no streaming: {str(e)}"
            })
