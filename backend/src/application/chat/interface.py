from abc import ABC, abstractmethod
from typing import Iterator, AsyncIterator, Dict, Any, Callable

from langchain_core.messages import BaseMessageChunk

from src.shared_kernel.schemas.message import Message
from ...adapters.llm_chat.interface import LlmChatAdapterInterface


class ChatApplicationInterface(ABC):
    def __init__(
            self,
            llm_provider: LlmChatAdapterInterface,
    ):
        self.llm_provider = llm_provider

    @abstractmethod
    def stream(self, messages: list[Message], **kwargs) -> Iterator[BaseMessageChunk]:
        pass

    @abstractmethod
    async def astream(self, messages: list[Message], **kwargs) -> AsyncIterator[BaseMessageChunk]:
        pass

    @abstractmethod
    async def process_websocket_chat(
        self,
        chat_request: Any,
        model_service: Any,
        send_message_callback: Callable[[Dict[str, Any]], None],
        is_stopped_callback: Callable[[], bool],
        log_chat_service: Any = None,
        session_id: int = None
    ) -> None:
        """
        Processa uma requisição de chat via WebSocket com streaming controlado

        Args:
            chat_request: Schema da requisição de chat
            model_service: Serviço de modelos LLM
            send_message_callback: Callback para enviar mensagens via WebSocket
            is_stopped_callback: Callback para verificar se streaming foi parado
        """
        pass

    @staticmethod
    def convert_messages_to_langchain_format(messages: list[Message]):
        msgs = [
            ("system", "Você é um assistente de IA do IEBT. Seu nome é Cognit AI."),
        ]

        for message in messages:
            if message.role == "user":
                # Verificar se é conteúdo multimodal (array) ou texto simples (string)
                if isinstance(message.content, list):
                    # Conteúdo multimodal - passar diretamente para LangChain
                    msgs.append(("human", message.content))
                else:
                    # Conteúdo de texto simples
                    msgs.append(("human", message.content))
            elif message.role == "assistant" or message.role == "ai":
                # Assistente sempre retorna texto simples
                msgs.append(("assistant", message.content))

        return msgs
