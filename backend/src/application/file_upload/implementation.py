"""
File Upload Application Implementation
"""

from typing import Optional, List, Dict, Any

from .interface import FileUploadApplicationInterface
from src.adapters.file_storage.interface import FileStorageInterface
from src.adapters.file_processor.interface import FileProcessorInterface
from src.shared_kernel.schemas.temp_file import TempFile


class FileUploadApplication(FileUploadApplicationInterface):
    """Implementation of file upload application services"""
    
    def __init__(
        self, 
        file_storage: FileStorageInterface,
        file_processor: FileProcessorInterface
    ):
        self.file_storage = file_storage
        self.file_processor = file_processor
    
    async def upload_file(
        self, 
        file_content: bytes, 
        filename: str, 
        mime_type: str
    ) -> TempFile:
        """Upload and store a file"""
        
        # Validate file type
        if not self.file_processor.is_supported_type(mime_type):
            raise ValueError(f"Unsupported file type: {mime_type}")
        
        # Validate file size
        file_size = len(file_content)
        max_size = 20 * 1024 * 1024  # 20MB
        if file_size > max_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {max_size})")
        
        if file_size == 0:
            raise ValueError("File is empty")
        
        # Validate filename
        if not filename or filename.strip() == "":
            raise ValueError("Filename cannot be empty")
        
        # Sanitize filename
        sanitized_filename = self._sanitize_filename(filename)
        
        # Create TempFile domain model
        temp_file = TempFile.create(
            original_filename=sanitized_filename,
            mime_type=mime_type,
            file_size=file_size,
            file_path="",  # Will be set by storage adapter
            expiry_hours=1  # Files expire in 1 hour
        )
        
        # Save file using storage adapter
        success = await self.file_storage.save_file(file_content, temp_file)
        if not success:
            raise ValueError("Failed to save file")
        
        return temp_file
    
    async def get_file_info(self, file_id: str) -> Optional[TempFile]:
        """Get file information by ID"""
        return await self.file_storage.get_file_info(file_id)
    
    async def process_file_for_llm(self, file_id: str, user_text: str, provider: str = "openai") -> Dict[str, Any]:
        """Process file for LLM consumption"""

        # Get file info
        temp_file = await self.file_storage.get_file_info(file_id)
        if not temp_file:
            raise ValueError(f"File not found: {file_id}")

        # Check if file is valid for LLM
        if not temp_file.is_valid_for_llm():
            if temp_file.is_expired():
                raise ValueError(f"File has expired: {file_id}")
            else:
                raise ValueError(f"File type not supported for LLM: {temp_file.mime_type}")

        # Get file content
        file_content = await self.file_storage.get_file(file_id)
        if not file_content:
            raise ValueError(f"File content not found: {file_id}")

        # Convert to base64
        base64_content = await self.file_processor.convert_to_base64(file_content, temp_file)

        # Create LLM message content
        llm_content = self.file_processor.create_llm_message_content(
            base64_content, temp_file, user_text, provider
        )

        return llm_content
    
    async def delete_file(self, file_id: str) -> bool:
        """Delete a file"""
        return await self.file_storage.delete_file(file_id)
    
    async def cleanup_expired_files(self) -> int:
        """Clean up expired files"""
        return await self.file_storage.cleanup_expired_files()
    
    async def list_files(self) -> List[TempFile]:
        """List all uploaded files"""
        return await self.file_storage.list_files()
    
    def get_supported_types(self) -> List[str]:
        """Get list of supported MIME types"""
        return list(self.file_processor.get_supported_types())
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to prevent security issues
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        import re
        import os
        
        # Get just the filename, no path
        filename = os.path.basename(filename)
        
        # Remove or replace dangerous characters
        # Keep only alphanumeric, dots, hyphens, underscores
        sanitized = re.sub(r'[^a-zA-Z0-9.\-_]', '_', filename)
        
        # Ensure it's not empty and has reasonable length
        if not sanitized or sanitized == '.':
            sanitized = 'unnamed_file'
        
        # Limit length
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:250] + ext
        
        return sanitized
    
    async def get_file_stats(self) -> Dict[str, Any]:
        """
        Get statistics about uploaded files
        
        Returns:
            Dictionary with file statistics
        """
        files = await self.list_files()
        
        total_files = len(files)
        total_size = sum(f.file_size for f in files)
        expired_files = sum(1 for f in files if f.is_expired())
        
        # Group by MIME type
        by_type = {}
        for file in files:
            mime_type = file.mime_type
            if mime_type not in by_type:
                by_type[mime_type] = {'count': 0, 'size': 0}
            by_type[mime_type]['count'] += 1
            by_type[mime_type]['size'] += file.file_size
        
        return {
            'total_files': total_files,
            'total_size_bytes': total_size,
            'expired_files': expired_files,
            'active_files': total_files - expired_files,
            'by_type': by_type,
            'supported_types': self.get_supported_types()
        }
