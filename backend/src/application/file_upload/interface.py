"""
File Upload Application Interface
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from src.shared_kernel.schemas.temp_file import TempFile


class FileUploadApplicationInterface(ABC):
    """Interface for file upload application services"""
    
    @abstractmethod
    async def upload_file(
        self, 
        file_content: bytes, 
        filename: str, 
        mime_type: str
    ) -> TempFile:
        """
        Upload and store a file
        
        Args:
            file_content: Binary content of the file
            filename: Original filename
            mime_type: MIME type of the file
            
        Returns:
            TempFile domain model with metadata
            
        Raises:
            ValueError: If file is invalid or unsupported
        """
        pass
    
    @abstractmethod
    async def get_file_info(self, file_id: str) -> Optional[TempFile]:
        """
        Get file information by ID
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            TempFile domain model, or None if not found
        """
        pass
    
    @abstractmethod
    async def process_file_for_llm(self, file_id: str, user_text: str, provider: str = "openai") -> Dict[str, Any]:
        """
        Process file for LLM consumption (convert to base64 and format)

        Args:
            file_id: Unique file identifier
            user_text: User's text message to include
            provider: LLM provider ("openai" or "anthropic")

        Returns:
            Dictionary with formatted content for LLM

        Raises:
            ValueError: If file not found or invalid
        """
        pass
    
    @abstractmethod
    async def delete_file(self, file_id: str) -> bool:
        """
        Delete a file
        
        Args:
            file_id: Unique file identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def cleanup_expired_files(self) -> int:
        """
        Clean up expired files
        
        Returns:
            Number of files cleaned up
        """
        pass
    
    @abstractmethod
    async def list_files(self) -> List[TempFile]:
        """
        List all uploaded files
        
        Returns:
            List of TempFile domain models
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """
        Get list of supported MIME types
        
        Returns:
            List of supported MIME types
        """
        pass
