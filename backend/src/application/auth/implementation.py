from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional

from .interface import AuthApplicationInterface
from src.adapters.auth.interface import AuthAdapterInterface
from src.application.user.interface import UserApplicationInterface
from src.adapters.repositories.user.models.user import User
from src.shared_kernel.utils.jwt_utils import create_token_for_user


class AuthApplication(AuthApplicationInterface):
    
    def __init__(self, auth_adapter: AuthAdapterInterface, user_app: UserApplicationInterface):
        self.auth_adapter = auth_adapter
        self.user_app = user_app
    
    async def get_login_url(self) -> str:
        return await self.auth_adapter.get_authorization_url()
    
    async def process_callback(self, code: str) -> Tuple[User, str, str]:
        token_data = await self.auth_adapter.exchange_code_for_token(code)
        access_token = token_data.get("access_token")
        id_token = token_data.get("id_token")

        if not access_token:
            raise ValueError("Token de acesso não recebido")

        user_info = await self.auth_adapter.get_user_info(access_token)

        if id_token:
            # IMPORTANTE: verify_id_token pode sobrescrever user_info
            # Vamos preservar a picture do get_user_info
            picture_from_userinfo = user_info.get("picture")
            user_info = await self.auth_adapter.verify_id_token(id_token)
            # Se o id_token não tem picture, usar a do userinfo
            if not user_info.get("picture") and picture_from_userinfo:
                user_info["picture"] = picture_from_userinfo

        user = self.user_app.find_or_create_user(user_info)

        # Extrair picture do user_info para incluir no JWT
        picture = user_info.get("picture")

        jwt_token = self.create_user_token(user, picture=picture)

        return user, jwt_token, picture
    
    def create_user_token(self, user: User, picture: str = None) -> str:
        return create_token_for_user(
            user_sub=user.google_sub,
            email=user.email,
            name=user.name,
            picture=picture
        )
