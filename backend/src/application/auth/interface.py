from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>
from sqlalchemy.orm import Session

from src.adapters.repositories.user.models.user import User


class AuthApplicationInterface(ABC):
    
    @abstractmethod
    async def get_login_url(self, redirect_uri: str = None) -> str:
        pass
    
    @abstractmethod
    async def process_callback(self, code: str, redirect_uri: str = None) -> Tuple[User, str, str]:
        pass
    
    @abstractmethod
    def create_user_token(self, user: User, extra_data: Dict[str, Any] = None) -> str:
        pass
