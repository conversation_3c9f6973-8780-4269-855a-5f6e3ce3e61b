from abc import ABC, abstractmethod
from typing import Optional
from typing import List, Optional

from src.shared_kernel.schemas.message import Message
from .schemas.chat_session import ChatSession, PaginatedChatSessions


class ChatHistoryApplicationInterface(ABC):
    @abstractmethod
    def new_session(self, user_id: int, title: Optional[str] = None) -> ChatSession:
        pass

    @abstractmethod
    def retrieve_session(self, user_id: int, session_id: str) -> ChatSession:
        pass

    @abstractmethod
    def retrieve_session_with_messages(self, user_id: int, session_id: str) -> ChatSession:
        """Retrieve a session with its messages including attached_files"""
        pass

    @abstractmethod
    def log_message(self, chat_session: ChatSession, message: Message, model_id: Optional[int] = None, **kwargs) -> None:
        pass

    @abstractmethod
    def delete_session(self, user_id: int, session_id: str) -> bool:
        pass

    @abstractmethod
    def create_websocket_session(self, user_id: int, client_id: str) -> ChatSession:
        """Cria uma nova sessão para conexão WebSocket"""
        pass

    @abstractmethod
    async def create_session_with_generated_title(self, user_id: int, first_message: str) -> ChatSession:
        """
        Cria uma nova sessão com título gerado automaticamente baseado na primeira mensagem

        Args:
            user_id: ID do usuário
            first_message: Primeira mensagem do usuário para gerar o título

        Returns:
            ChatSession criada com título gerado
        """
        pass

    @abstractmethod
    def finalize_websocket_session(self, session_id: int, user_id: int) -> bool:
        """Finaliza uma sessão WebSocket atualizando last_interaction"""
        pass

    @abstractmethod
    def get_session_by_id(self, session_id: int) -> Optional[ChatSession]:
        """Busca uma sessão pelo ID"""
        pass

    @abstractmethod
    def get_user_sessions_with_history(self, user_id: int, page: int = 1, per_page: int = 10) -> PaginatedChatSessions:
        """Busca sessões do usuário com histórico de mensagens e paginação"""
        pass

    @abstractmethod
    def update_session_title(self, user_id: int, session_id: int, title: str) -> bool:
        """Atualiza o título de uma sessão"""
        pass

    @abstractmethod
    def soft_delete_session(self, user_id: int, session_id: int) -> bool:
        """Faz soft delete de uma sessão"""
        pass

    @abstractmethod
    def update_session_interaction(self, session_id: int, user_id: int) -> bool:
        """Atualiza last_interaction de uma sessão"""
        pass

    @abstractmethod
    def get_recent_messages(self, session_id: int, limit: int = 5):
        """Busca as últimas mensagens de uma sessão para contexto"""
        pass
