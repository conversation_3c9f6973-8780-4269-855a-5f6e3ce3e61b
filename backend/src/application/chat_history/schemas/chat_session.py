from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime


@dataclass
class ChatMessage:
    id: int
    author: str  # "user" or "bot"
    content: str
    timestamp: datetime
    model_id: Optional[int] = None  # ID do modelo usado
    attached_files: Optional[List[Dict[str, str]]] = None  # [{"name": "file.pdf", "mime_type": "application/pdf"}]


@dataclass
class ChatSession:
    session_id: int
    user_id: int
    title: Optional[str] = None
    first_interaction: Optional[datetime] = None
    last_interaction: Optional[datetime] = None
    messages: Optional[List[ChatMessage]] = None


@dataclass
class PaginatedChatSessions:
    sessions: List[ChatSession]
    total: int
    page: int
    per_page: int
    total_pages: int
