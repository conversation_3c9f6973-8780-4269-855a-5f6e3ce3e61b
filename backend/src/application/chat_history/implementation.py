from datetime import datetime
from typing import Optional, List, Dict, Any
import math

from src.shared_kernel.schemas.message import Message
from .exceptions.cannot_create_session import CannotCreateSession
from .exceptions.cannot_log_message import CannotLogMessage
from .exceptions.no_session_found import NoSessionFound
from .interface import ChatHistoryApplicationInterface
from .schemas.chat_session import ChatSession, ChatMessage, PaginatedChatSessions

from src.adapters.repositories.chat_history.interface import ChatSessionRepositoryInterface, ChatMessageRepositoryInterface
from src.adapters.repositories.chat_history.models.chat_message import Author
from src.adapters.llm_chat.provider.openai import LlmChatAdapterOpenAI
from src.adapters.repositories.chat_history.models.chat_message import ChatMessage as ChatMessageModel
from src.adapters.repositories.chat_history.models.chat_session import ChatSession as ChatSessionModel



class ChatHistoryApplication(ChatHistoryApplicationInterface):
    def __init__(self,
                 chat_session_repo: ChatSessionRepositoryInterface,
                 chat_message_repo: ChatMessageRepositoryInterface):
        self._chat_session_repo = chat_session_repo
        self._chat_message_repo = chat_message_repo
        # Initialize LLM adapter for title generation
        self._title_llm_adapter = LlmChatAdapterOpenAI(model="gpt-4o-mini")

    def new_session(self, user_id: int, title: Optional[str] = None) -> ChatSession:
        try:
            db_session = self._chat_session_repo.create(user_id, title)

            return ChatSession(
                session_id=db_session.id,
                user_id=user_id,
                title=db_session.title,
                first_interaction=db_session.first_interaction,
                last_interaction=db_session.last_interaction
            )
        except Exception as e:
            raise CannotCreateSession(f"Failed to create session: {str(e)}")


    def retrieve_session(self, user_id: int, session_id: str) -> ChatSession:
        db_session = self._chat_session_repo.find_by_id_and_user(int(session_id), user_id)

        if not db_session:
            raise NoSessionFound(f"Chat session {session_id} not found for user {user_id}")

        return ChatSession(
            session_id=db_session.id,
            user_id=user_id,
            title=db_session.title,
            first_interaction=db_session.first_interaction,
            last_interaction=db_session.last_interaction
        )

    def retrieve_session_with_messages(self, user_id: int, session_id: str) -> ChatSession:
        """Retrieve a session with its messages including attached_files"""
        db_session = self._chat_session_repo.find_by_id_and_user(int(session_id), user_id)

        if not db_session:
            raise NoSessionFound(f"Chat session {session_id} not found for user {user_id}")

        # Buscar mensagens da sessão
        db_messages = self._chat_message_repo.find_by_session(db_session.id)

        # Converter mensagens
        messages = [
            ChatMessage(
                id=msg.id,
                author="user" if msg.author == Author.USER else "bot",
                content=msg.content,
                timestamp=msg.timestamp,
                model_id=msg.model_id,
                attached_files=msg.attached_files
            )
            for msg in db_messages
        ]

        return ChatSession(
            session_id=db_session.id,
            user_id=user_id,
            title=db_session.title,
            first_interaction=db_session.first_interaction,
            last_interaction=db_session.last_interaction,
            messages=messages
        )


    def log_message(
        self,
        chat_session: ChatSession,
        message: Message,
        model_id: Optional[int] = None,
        attached_files: Optional[List[Dict[str, str]]] = None,
        **kwargs
    ) -> None:
        # Verificar se a sessão existe
        db_session = self._chat_session_repo.find_by_id_and_user(
            chat_session.session_id,
            chat_session.user_id
        )

        if not db_session:
            raise CannotLogMessage(f"Chat session {chat_session.session_id} not found for user {chat_session.user_id}")

        # Determinar o autor
        if message.role == "user":
            author = Author.USER.value
        else:
            author = Author.BOT.value

        # Criar mensagem
        try:
            self._chat_message_repo.create(
                session_id=chat_session.session_id,
                author=author,
                content=message.content,
                model_id=model_id,
                attached_files=attached_files
            )
        except Exception as e:
            raise CannotLogMessage(f"Failed to log message: {str(e)}")

    def delete_session(self, user_id: int, session_id: str) -> bool:
        """Soft delete de uma sessão de chat"""
        db_session = self._chat_session_repo.find_by_id_and_user(int(session_id), user_id)

        if not db_session:
            return False

        self._chat_session_repo.soft_delete(db_session)
        return True

    def create_websocket_session(self, user_id: int, client_id: str) -> ChatSession:
        """Cria uma nova sessão para conexão WebSocket"""
        title = f"WebSocket Session - {client_id}"
        return self.new_session(user_id, title=title)

    async def create_session_with_generated_title(self, user_id: int, first_message: str) -> ChatSession:
        """
        Cria uma nova sessão com título gerado automaticamente baseado na primeira mensagem

        Args:
            user_id: ID do usuário
            first_message: Primeira mensagem do usuário para gerar o título

        Returns:
            ChatSession criada com título gerado
        """
        try:
            # Gerar título usando GPT-4o-mini
            title = await self._generate_session_title(first_message)

            # Criar sessão com o título gerado
            return self.new_session(user_id, title=title)

        except Exception as e:
            # Em caso de erro, criar sessão com título padrão
            print(f"Error generating title: {e}")
            return self.new_session(user_id, title="Nova Conversa")

    async def _generate_session_title(self, first_message: str) -> str:
        """
        Generate a session title based on the first user message using GPT-4o-mini

        Args:
            first_message: The first message sent by the user

        Returns:
            A title string with maximum 25 characters
        """
        try:
            # Create a prompt for title generation
            prompt = f"""Baseado na seguinte mensagem do usuário, gere um título curto e descritivo para a conversa.

            Mensagem do usuário: "{first_message}"

            Regras:
            - Máximo 25 caracteres
            - Seja conciso e descritivo
            - Use apenas o essencial da mensagem
            - Não use aspas ou caracteres especiais
            - Responda apenas com o título, sem explicações

            Título:"""

            # Create messages for the LLM
            messages = [Message(role="user", content=prompt)]

            # Convert to LangChain format
            messages_as_tuples = self._convert_messages_to_langchain_format(messages)

            # Get response from LLM
            client = self._title_llm_adapter.client
            response = await client.invoke(input=messages_as_tuples)

            # Extract and clean the title
            title = response.content.strip()

            # Ensure maximum 50 characters
            if len(title) > 50:
                return title[:47] + "..."

            # Fallback if title is empty or too short
            if not title or len(title) < 3:
                return first_message

        except Exception as e:
            print(f"Error generating title: {e}")
            # Return fallback title in case of error
            return first_message

    def _convert_messages_to_langchain_format(self, messages: list[Message]) -> list[tuple[str, str]]:
        """Convert Message objects to LangChain format"""
        return [(msg.role, msg.content) for msg in messages]

    def finalize_websocket_session(self, session_id: int, user_id: int) -> bool:
        """Finaliza uma sessão WebSocket atualizando last_interaction"""
        try:
            # Usar o repository interface para atualizar last_interaction
            return self._chat_session_repo.update_last_interaction(session_id, user_id)
        except Exception:
            return False

    def get_session_by_id(self, session_id: int, user_id: Optional[int] = None) -> Optional[ChatSession]:
        """Busca uma sessão pelo ID"""
        try:
            if user_id:
                # Se temos user_id, usar o método seguro
                db_session = self._chat_session_repo.find_by_id_and_user(session_id, user_id)
            else:
                # Fallback: buscar todas as sessões e filtrar por ID (menos eficiente mas funciona)
                # Isso é para compatibilidade com código existente
                db_sessions, _ = self._chat_session_repo.find_by_user_paginated(1, 1, 1000)  # Buscar muitas sessões
                db_session = next((s for s in db_sessions if s.id == session_id), None)

            if not db_session:
                return None

            return ChatSession(
                session_id=db_session.id,
                user_id=db_session.user_id,
                title=db_session.title,
                first_interaction=db_session.first_interaction,
                last_interaction=db_session.last_interaction
            )
        except Exception:
            return None

    def get_user_sessions_with_history(self, user_id: int, page: int = 1, per_page: int = 10) -> PaginatedChatSessions:
        """Busca sessões do usuário com histórico de mensagens e paginação"""
        # Buscar sessões com paginação
        db_sessions, total = self._chat_session_repo.find_by_user_paginated(user_id, page, per_page)

        # Converter para ChatSession com mensagens
        sessions = []
        for db_session in db_sessions:
            # Buscar mensagens da sessão
            db_messages = self._chat_message_repo.find_by_session(db_session.id)

            # Converter mensagens
            messages = [
                ChatMessage(
                    id=msg.id,
                    author="user" if msg.author == Author.USER else "bot",
                    content=msg.content,
                    timestamp=msg.timestamp,
                    model_id=msg.model_id,
                    attached_files=msg.attached_files
                )
                for msg in db_messages
            ]

            # Criar ChatSession com mensagens
            session = ChatSession(
                session_id=db_session.id,
                user_id=db_session.user_id,
                title=db_session.title,
                first_interaction=db_session.first_interaction,
                last_interaction=db_session.last_interaction,
                messages=messages
            )
            sessions.append(session)

        # Calcular total de páginas
        total_pages = math.ceil(total / per_page) if total > 0 else 1

        return PaginatedChatSessions(
            sessions=sessions,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages
        )

    def update_session_title(self, user_id: int, session_id: int, title: str) -> bool:
        """Atualiza o título de uma sessão"""
        try:
            db_session = self._chat_session_repo.find_by_id_and_user(session_id, user_id)

            if not db_session:
                return False

            self._chat_session_repo.update_title(db_session, title)
            return True

        except Exception:
            return False

    def soft_delete_session(self, user_id: int, session_id: int) -> bool:
        """Faz soft delete de uma sessão"""
        try:
            db_session = self._chat_session_repo.find_by_id_and_user(session_id, user_id)

            if not db_session:
                return False

            self._chat_session_repo.soft_delete(db_session)
            return True

        except Exception:
            return False

    def update_session_interaction(self, session_id: int, user_id: int) -> bool:
        """Atualiza last_interaction de uma sessão"""
        try:
            # Usar o repository interface para atualizar last_interaction
            return self._chat_session_repo.update_last_interaction(session_id, user_id)
        except Exception:
            return False

    def get_recent_messages(self, session_id: int, limit: int = 5) -> list[Message]:
        """Busca as últimas mensagens de uma sessão para contexto"""
        try:
            # Buscar todas as mensagens da sessão
            db_messages = self._chat_message_repo.find_by_session(session_id)

            # Pegar apenas as últimas 'limit' mensagens
            recent_messages = db_messages[-limit:] if len(db_messages) > limit else db_messages

            # Converter para formato de domínio
            messages = []
            for db_message in recent_messages:
                role = "user" if db_message.author == Author.USER else "assistant"
                message = Message(role=role, content=db_message.content)
                messages.append(message)

            return messages

        except Exception:
            return []
