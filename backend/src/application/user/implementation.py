from typing import Dict, Any

from .interface import UserApplicationInterface
from src.adapters.repositories.user.interface import UserRepositoryInterface
from src.adapters.repositories.user.models.user import User


class UserApplication(UserApplicationInterface):

    def __init__(self, user_repo: UserRepositoryInterface):
        self._user_repo = user_repo

    def find_or_create_user(self, user_info: Dict[str, Any]) -> User:
        google_sub = user_info.get("sub")
        email = user_info.get("email")
        name = user_info.get("name", "")

        if not google_sub or not email:
            raise ValueError("Informações obrigatórias do usuário não fornecidas")

        # Busca usuário existente
        user = self._user_repo.find_by_google_sub(google_sub)

        if not user:
            # Cria novo usuário
            user = self._user_repo.create(user_info)

        return user

