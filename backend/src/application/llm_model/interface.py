from abc import ABC, abstractmethod
from typing import List, Optional
from src.adapters.repositories.llm_model.models.llm_model import LLMModel


class LLMModelApplicationInterface(ABC):
    
    @abstractmethod
    async def get_all_models(self, active_only: bool = True) -> List[LLMModel]:
        pass
    
    @abstractmethod
    async def get_model_by_id(self, model_id: int) -> Optional[LLMModel]:
        pass
