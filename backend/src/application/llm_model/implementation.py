from typing import List, Optional

from .interface import LLMModelApplicationInterface
from src.adapters.repositories.llm_model.interface import LLMModelRepositoryInterface
from src.adapters.repositories.llm_model.models.llm_model import LLMModel


class LLMModelApplication(LLMModelApplicationInterface):

    def __init__(self, llm_model_repo: LLMModelRepositoryInterface):
        self._llm_model_repo = llm_model_repo

    async def get_all_models(self, active_only: bool = True) -> List[LLMModel]:
        return self._llm_model_repo.get_all(active_only)

    async def get_model_by_id(self, model_id: int) -> Optional[LLMModel]:
        return self._llm_model_repo.get_by_id(model_id)
