import os
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()


@dataclass
class Config:

    DEPLOY_ENV = os.environ.get('DEPLOY_ENV', 'development')
    PRODUCTION = os.environ.get('DEPLOY_ENV') == 'production' or os.environ.get('DEPLOY_ENV') == 'prod'

    @dataclass
    class OpenAI:
        API_KEY = os.environ.get('OPENAI_API_KEY')

    @dataclass
    class Anthropic:
        API_KEY = os.environ.get('ANTHROPIC_API_KEY')

    @dataclass
    class IEBT:
        class SSO:
            API_AUTH_BASE_URL = os.environ.get('IEBT_API_AUTH_URL','https://accounts.google.com/o/oauth2/')
            CLIENT_ID = os.environ.get('IEBT_API_AUTH_CLIENT_ID')
            CLIENT_SECRET = os.environ.get('IEBT_API_AUTH_CLIENT_SECRET')
            REDIRECT_URI = os.environ.get('IEBT_API_AUTH_REDIRECT_URI', 'http://localhost:8501/')
            API_EXCHANGE_TOKEN_URL = os.environ.get('IEBT_API_EXCHANGE_TOKEN_URL','https://oauth2.googleapis.com/token')

    @dataclass
    class Database:
        URL = os.environ.get('DB_URL', 'postgresql://cognit:cognit123@localhost:5432/cognit-ai')

    @dataclass
    class JWT:
        SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
        ALGORITHM = os.environ.get('JWT_ALGORITHM', 'HS256')
        ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get('JWT_ACCESS_TOKEN_EXPIRE_MINUTES', '30'))

    @dataclass
    class CORS:
        ORIGINS = os.environ.get('CORS_ORIGINS', 'http://localhost:5173').split(',')