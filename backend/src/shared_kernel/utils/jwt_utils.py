from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from jose import jwt
from src.shared_kernel.config import Config

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Cria um token JWT de acesso.
    
    Args:
        data: Dados para incluir no payload do token
        expires_delta: Tempo de expiração customizado
    
    Returns:
        Token JWT codificado
    """
    to_encode = data.copy()
    
    # Define tempo de expiração
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=Config.JWT.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Adiciona campos padrão do JWT
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access"
    })
    
    # Codifica o token
    encoded_jwt = jwt.encode(
        to_encode, 
        Config.JWT.SECRET_KEY, 
        algorithm=Config.JWT.ALGORITHM
    )
    
    return encoded_jwt

def create_token_for_user(user_sub: str, email: str, name: str = None, picture: str = None) -> str:
    """
    Cria um token para um usuário específico.

    Args:
        user_sub: Subject (identificador único) do usuário
        email: Email do usuário
        name: Nome do usuário (opcional)
        picture: URL da foto do usuário (opcional)

    Returns:
        Token JWT codificado
    """
    token_data = {
        "sub": user_sub,
        "email": email
    }

    # Adiciona campos opcionais se fornecidos
    if name:
        token_data["name"] = name
    if picture:
        token_data["picture"] = picture

    return create_access_token(token_data)
