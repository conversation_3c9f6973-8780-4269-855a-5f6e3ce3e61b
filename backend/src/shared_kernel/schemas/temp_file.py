"""
Domain model for temporary files
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import uuid


@dataclass
class TempFile:
    """Domain model for temporary files"""
    
    file_id: str
    original_filename: str
    stored_filename: str
    mime_type: str
    file_size: int
    upload_timestamp: datetime
    expiry_timestamp: datetime
    file_path: str
    
    @classmethod
    def create(
        cls,
        original_filename: str,
        mime_type: str,
        file_size: int,
        file_path: str,
        expiry_hours: int = 1
    ) -> 'TempFile':
        """Create a new temporary file instance"""
        from datetime import timedelta
        
        now = datetime.utcnow()
        file_id = str(uuid.uuid4())
        stored_filename = f"{file_id}_{original_filename}"
        
        return cls(
            file_id=file_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            mime_type=mime_type,
            file_size=file_size,
            upload_timestamp=now,
            expiry_timestamp=now + timedelta(hours=expiry_hours),
            file_path=file_path
        )
    
    def is_expired(self) -> bool:
        """Check if file has expired"""
        return datetime.utcnow() > self.expiry_timestamp
    
    def is_valid_for_llm(self) -> bool:
        """Check if file is valid for LLM processing"""
        if self.is_expired():
            return False
        
        # Check supported MIME types for GPT-4o
        supported_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'text/plain',
            'text/markdown',
            'application/json'
        ]
        
        return self.mime_type in supported_types
    
    def get_file_extension(self) -> str:
        """Get file extension from original filename"""
        if '.' in self.original_filename:
            return self.original_filename.split('.')[-1].lower()
        return ''
    
    def to_dict(self) -> dict:
        """Convert to dictionary for API responses"""
        return {
            'file_id': self.file_id,
            'filename': self.original_filename,
            'file_type': self.mime_type,
            'file_size': self.file_size,
            'upload_timestamp': self.upload_timestamp.isoformat(),
            'expiry_timestamp': self.expiry_timestamp.isoformat()
        }
