from dataclasses import dataclass
from typing import Literal, Optional, List, Dict


@dataclass
class Message:
    role: Literal['user'] | Literal['ai']
    content: str
    attached_files: Optional[List[Dict[str, str]]] = None  # [{"name": "file.pdf", "mime_type": "application/pdf"}]

    @property
    def author(self) -> str:
        return self.role

    @property
    def has_attachments(self) -> bool:
        """Check if message has file attachments"""
        return self.attached_files is not None and len(self.attached_files) > 0

    @property
    def attachment_count(self) -> int:
        """Get number of attachments"""
        return len(self.attached_files) if self.attached_files else 0
