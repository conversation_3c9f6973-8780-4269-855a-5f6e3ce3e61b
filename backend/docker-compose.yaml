services:
  postgres:
    image: postgres:15
    container_name: cognit-postgres
    environment:
      POSTGRES_DB: cognit_db
      POSTGRES_USER: cognit_user
      POSTGRES_PASSWORD: cognit_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cognit_user -d cognit_db"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - cognit-network

  app:
    build: .
    container_name: cognit-app
    ports:
      - "8501:8501"
    env_file:
      - .env
    environment:
      - DB_URL=******************************************************/cognit_db
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - cognit-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  cognit-network:
    driver: bridge