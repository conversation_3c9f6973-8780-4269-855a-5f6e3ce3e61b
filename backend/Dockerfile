# STEP 1: Install poetry and dependencies
FROM python:3.13 AS builder

RUN pip install poetry==2.1.2

ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

WORKDIR /app
COPY pyproject.toml poetry.lock ./
RUN poetry install --no-root && rm -rf $POETRY_CACHE_DIR

# STEP 2: Build the runtime image
FROM python:3.13-slim AS runtime

RUN apt-get update && apt-get install -y --no-install-recommends curl

ENV VIRTUAL_ENV="/app/.venv" \
    PATH="/app/.venv/bin:$PATH"

COPY --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}

WORKDIR /app
COPY . .

# Criar diretório para arquivos temporários (para ambientes sem docker-compose)
RUN mkdir -p /tmp/cognit_files && chmod 755 /tmp/cognit_files

EXPOSE 8501

HEALTHCHECK CMD curl --fail http://localhost:8501/health

CMD .venv/bin/alembic upgrade head && uvicorn api:app --host 0.0.0.0 --port 8501 --reload