[project]
name = "iebt-cognit-ai"
version = "0.1.0"
description = ""
authors = [
    {name = "Italo Lelis", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<4.0"


[tool.poetry]
package-mode = false


[tool.poetry.dependencies]
fastapi = "^0.115.12"
python-dotenv = "^1.1.0"
langchain = "^0.3.24"
langchain-openai = "^0.3.14"
langchain-anthropic = "^0.3.13"
sqlalchemy = "^2.0.41"
psycopg2-binary = "^2.9.10"
alembic-postgresql-enum = "^1.7.0"
python-jose = "^3.3.0"
cryptography = "^43.0.0"
httpx = "^0.28.0"
authlib = "^1.3.1"
google-auth = "^2.29.0"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
aiofiles = "^24.1.0"
python-multipart = "^0.0.6"


[tool.poetry.group.dev.dependencies]
alembic = "^1.16.4"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
