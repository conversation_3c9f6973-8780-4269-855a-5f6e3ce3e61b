{"permissions": {"allow": ["Bash(docker logs:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(timeout 30 make start)", "<PERSON><PERSON>(docker-compose:*)", "Bash(make status:*)", "<PERSON><PERSON>(make:*)", "Bash(DB_URL=\"postgresql://cognit_user:cognit_password@localhost:5432/cognit_db\" poetry run alembic upgrade head)", "Bash(DB_URL=\"postgresql://cognit_user:cognit_password@localhost:5432/cognit_db\" poetry run python src/infrastructure/database/seeders/run_seeders.py)", "Bash(DB_URL=\"postgresql://cognit_user:cognit_password@localhost:5432/cognit_db\" poetry run python -c \"\nfrom src.infrastructure.database.session import get_db_session\nfrom src.adapters.repositories.llm_model.models.llm_model import LLMModel\nfrom contextlib import closing\n\nwith closing(get_db_session()) as gen:\n    db = next(gen)\n    models = db.query(LLMModel).all()\n    print(f''Total de modelos: {len(models)}'')\n    for model in models:\n        status = ''✅ ATIVO'' if model.is_active else ''❌ INATIVO''\n        print(f''{status} {model.name} ({model.provider}) - $model.price_usd/1k tokens'')\n\")", "Bash(DB_URL=\"postgresql://cognit_user:cognit_password@localhost:5432/cognit_db\" poetry run python /tmp/verify_models.py)"], "deny": []}}