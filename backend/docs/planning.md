# Plano de Refatoração - Arquitetura Híbrida (Hexagonal + DDD) - Backend Cognit AI

## Contexto Geral

Este documento contém o plano detalhado de refatoração do backend da aplicação Cognit AI para implementar uma **arquitetura híbrida** que combina **Arquitetura Hexagonal** com **conceitos seletivos de DDD**, mantendo a estrutura atual mas organizando melhor as responsabilidades.

### Estrutura Atual
```
backend/
├── database/                    # ❌ PROBLEMA: Deveria estar em infrastructure
│   ├── models/                 # SQLAlchemy models
│   ├── migrations/            # Alembic migrations
│   └── seeders/               # Database seeders
├── src/
│   ├── adapters/              # ✅ Comunicação com serviços externos
│   ├── application/           # ⚠️  Regras de negócio (mas com violações)
│   ├── presentation/          # ✅ DTOs e rotas (mas com regras de negócio)
│   └── shared_kernel/         # ✅ Código compartilhado
└── tests/
```

### Tecnologias Utilizadas
- **Framework**: FastAPI 0.115.12
- **Database**: PostgreSQL com SQLAlchemy 2.0
- **Authentication**: Google OAuth + JWT
- **LLM Providers**: OpenAI e Anthropic (via LangChain)
- **WebSocket**: Suporte nativo do FastAPI
- **Migration**: Alembic

### Arquitetura Alvo - Responsabilidades por Camada

#### **🔌 Adapters** - Comunicação Externa
- **Banco de dados**: Repositories que implementam interfaces
- **APIs externas**: LLM providers, OAuth, etc.
- **Sem regras de negócio**: Apenas tradução de dados

#### **🧠 Application** - Regras de Negócio
- **Implementação**: Lógica de negócio pura
- **Interfaces**: Contratos para adapters
- **Orquestração**: Coordena casos de uso

#### **🎯 Presentation** - DTOs e Rotas
- **DTOs**: Validação de entrada/saída
- **Routers**: Endpoints sem lógica de negócio
- **Dependency Injection**: Configuração de dependências

#### **🏗️ Infrastructure** - Infraestrutura
- **Database**: Models, migrations, session management
- **Configurações**: Setup de infraestrutura

#### **🔧 Shared Kernel** - Código Compartilhado
- **Configurações**: Settings globais
- **Utilities**: Funções auxiliares
- **Schemas**: Modelos compartilhados

## Violações da Arquitetura Identificadas

### 1. **Application com Dependências de Infrastructure**
**Problema**: Application Services dependem diretamente de SQLAlchemy.

**Evidências Críticas**:
```python
# ❌ src/application/chat_history/implementation.py:8
from database import get_db_session

# ❌ src/application/user/implementation.py:8-20
def __init__(self, db: Session):
    self.db = db
user = self.db.query(User).filter(User.google_sub == google_sub).first()

# ❌ src/application/llm_model/implementation.py:14
query = select(LLMModel)
```

**Violação**: Application conhece detalhes de persistência (Session, queries SQL).
**Impacto**: Alto - impossibilita testes isolados e viola inversão de dependências.

### 2. **Presentation com Regras de Negócio**
**Problema**: Camada de apresentação contém lógica que deveria estar na aplicação.

**Evidências Críticas**:
```python
# ❌ src/presentation/web/pages/chat.py:3-6
from src.adapters.llm_chat.interface import LlmChatAdapterInterface
from src.adapters.llm_chat.provider.anthropic import LlmChatAdapterAnthropic
from src.adapters.llm_chat.provider.openai import LlmChatAdapterOpenAI
from src.application.chat import ChatApplication

# ❌ src/presentation/api/dependencies/auth.py:8-9
from database.database import get_db_session
from database.models.user import User
```

**Violação**: Presentation importa diretamente adapters e database models.
**Impacto**: Médio - mistura responsabilidades e dificulta manutenção.

### 3. **Database fora da Infrastructure**
**Problema**: Modelos e configurações de banco fora da camada de infraestrutura.

**Evidências Críticas**:
```
❌ database/models/          # Deveria estar em src/infrastructure/database/
❌ database/database.py      # Session management fora da infrastructure
❌ database/migrations/      # Migrações fora da infrastructure
```

**Violação**: Infrastructure espalhada fora da estrutura adequada.
**Impacto**: Médio - dificulta organização e manutenção da infraestrutura.

### 4. **Adapters sem Interfaces Adequadas**
**Problema**: Alguns adapters não seguem padrão de interface consistente.

**Evidências Críticas**:
```python
# ✅ src/adapters/auth/interface.py - BOM
class AuthAdapterInterface(ABC):

# ✅ src/adapters/llm_chat/interface.py - BOM
class LlmChatAdapterInterface(ABC):

# ❌ Falta interface para Repository Adapters
# ❌ Application Services fazem queries diretas
```

**Violação**: Faltam interfaces para abstrair persistência.
**Impacto**: Alto - acopla application à tecnologia de banco específica.

### 3. **Gerenciamento Problemático de Sessões de Banco**
**Problema**: Múltiplas formas de gerenciar sessões, potencial vazamento de conexões.

**Evidências**:
- `src/presentation/api/routers/websocket_chat.py:33` cria sessão manualmente com `next(get_db_session())`
- `src/application/log_chat/implementation.py:28` também usa `next(get_db_session())`
- Não há centralização do gerenciamento de sessões

**Impacto**: Alto - pode causar vazamentos de conexão e inconsistências.

### 4. **Duplicação de Código**
**Problema**: Lógica similar repetida em múltiplos lugares.

**Evidências**:
- Autenticação JWT duplicada entre REST (`get_current_user`) e WebSocket (`get_current_user_websocket`)
- Validação de modelos LLM repetida em `chat.py` e `websocket_chat.py`
- Exception handling similar espalhado por toda aplicação

**Impacto**: Médio - aumenta custo de manutenção e chance de bugs.

### 5. **Responsabilidades Mal Definidas**
**Problema**: Classes com múltiplas responsabilidades violando SRP.

**Evidências**:
- `ApplicationChat` faz tanto chat quanto WebSocket management
- `WebSocketChatManager` mistura gerenciamento de conexões com lógica de negócio
- `ApplicationLogChat` faz CRUD, logging e gerenciamento de sessões WebSocket

**Impacto**: Alto - dificulta testes unitários e manutenção.

### 6. **Problemas na Organização de Arquivos**
**Problema**: Estrutura de pastas não reflete a arquitetura pretendida.

**Evidências**:
- Interfaces misturadas com implementations
- Factories misturadas com adapters
- Exceções espalhadas sem hierarquia clara

**Impacto**: Baixo a Médio - dificulta navegação e descobrimento de código.

## Plano de Refatoração - Arquitetura Híbrida

### **Fase 1: Reorganização da Infrastructure (1-2 semanas)**

#### 1.1 Criação da Camada Infrastructure
**Objetivo**: Mover database para dentro da estrutura src/.

**Database Infrastructure**:
- [ ] Criar `src/infrastructure/`
- [ ] Criar `src/infrastructure/database/`
- [ ] Mover `database/models/*` → `src/infrastructure/database/models/*`
- [ ] Mover `database/database.py` → `src/infrastructure/database/session.py`
- [ ] Mover `database/migrations/*` → `src/infrastructure/database/migrations/*`
- [ ] Mover `database/seeders/*` → `src/infrastructure/database/seeders/*`
- [ ] Atualizar `alembic.ini` para nova localização
- [ ] Atualizar imports em toda aplicação

#### 1.2 Criação de Repository Adapters
**Objetivo**: Mover lógica de acesso a dados para camada de adapters organizados por módulo.

**Repository Modules (em Adapters)**:
- [ ] Criar `src/adapters/repositories/`
- [ ] Criar `src/adapters/repositories/user/`
  - [ ] `interface.py` (UserRepositoryInterface)
  - [ ] `repository.py` (UserRepository - implementa interface)
- [ ] Criar `src/adapters/repositories/chat_history/`
  - [ ] `interface.py` (ChatSessionRepositoryInterface, ChatMessageRepositoryInterface)
  - [ ] `repository.py` (ChatSessionRepository, ChatMessageRepository)
- [ ] Criar `src/adapters/repositories/llm_model/`
  - [ ] `interface.py` (LLMModelRepositoryInterface)
  - [ ] `repository.py` (LLMModelRepository - implementa interface)

#### 1.3 Refatoração dos Application Services (Mantendo Estrutura Atual)
**Objetivo**: Application Services usam repository adapters via dependency injection.

**Dependency Injection via Constructor**:
```python
# ✅ Padrão correto - mantendo estrutura atual
# src/application/chat_history/implementation.py
from src.adapters.repositories.chat_history.interface import ChatSessionRepositoryInterface, ChatMessageRepositoryInterface

class ChatHistoryApplication(ChatHistoryApplicationInterface):
    def __init__(self,
                 chat_session_repo: ChatSessionRepositoryInterface,
                 chat_message_repo: ChatMessageRepositoryInterface):
        self._chat_session_repo = chat_session_repo
        self._chat_message_repo = chat_message_repo
```

**Refatorações Específicas (mantendo módulos atuais)**:
- [ ] Refatorar `src/application/chat_history/implementation.py` para usar repository adapters
- [ ] Refatorar `src/application/user/implementation.py` para usar repository adapters
- [ ] Refatorar `src/application/llm_model/implementation.py` para usar repository adapters
- [ ] Manter `src/application/*/interface.py` como estão (Application Service interfaces)
- [ ] Remover todos os imports diretos de `database.*` dos implementation.py
- [ ] Remover `Session` dos construtores dos Application Services
- [ ] Mover toda lógica SQL para repository adapters

### **Fase 2: Limpeza da Camada Presentation (1 semana)**

#### 2.1 Remoção de Regras de Negócio da Presentation
**Objetivo**: Presentation deve conter apenas DTOs e rotas.

**Refatoração de Dependencies**:
- [ ] Refatorar `src/presentation/api/dependencies/auth.py`
- [ ] Remover imports diretos de `database.models.user`
- [ ] Mover lógica de verificação de token para Application
- [ ] Usar apenas Application Services via dependency injection

**Refatoração de Web Pages**:
- [ ] Refatorar `src/presentation/web/pages/chat.py`
- [ ] Remover imports diretos de adapters (`LlmChatAdapterInterface`)
- [ ] Usar apenas Application Services
- [ ] Mover configuração de LLM providers para Application

**Schemas e DTOs**:
- [ ] Manter `src/presentation/api/schemas/` apenas para validação
- [ ] Garantir que schemas não contenham lógica de negócio
- [ ] Usar schemas apenas para entrada/saída de dados

#### 2.2 Configuração de Dependency Injection
**Objetivo**: Configurar injeção adequada seguindo a arquitetura.

**Factory Pattern**:
- [ ] Refatorar `src/presentation/api/dependencies/*_factory.py`
- [ ] Implementar injeção via constructor:
```python
def create_chat_history_application(session: Session) -> ChatHistoryApplicationInterface:
    chat_session_repo = ChatSessionRepository(session)
    chat_message_repo = ChatMessageRepository(session)
    return ChatHistoryApplication(chat_session_repo, chat_message_repo)
```

**Router Dependencies**:
- [ ] Garantir que routers dependem apenas de Application Interfaces
- [ ] Remover imports diretos de infrastructure dos routers
- [ ] Usar dependency injection para todos os services

**WebSocket Refactoring**:
- [ ] Refatorar `src/presentation/api/routers/websocket_chat.py`
- [ ] Remover `next(get_db_session())` e usar dependency injection
- [ ] Aplicar padrão de dependency injection consistente

### **Fase 3: Validação e Testes (1 semana)**

#### 3.1 Testes de Arquitetura
**Objetivo**: Garantir que arquitetura está corretamente implementada.

**Testes de Dependências**:
- [ ] Criar testes que validam que Application não importa Infrastructure
- [ ] Verificar que todos os Application Services usam apenas interfaces
- [ ] Validar que Presentation não contém regras de negócio

**Testes Unitários Isolados**:
- [ ] Criar mocks para todos os Repository Interfaces
- [ ] Testar Application Services sem dependências externas
- [ ] Validar que lógica de negócio está isolada na Application

#### 3.2 Documentação da Arquitetura
**Objetivo**: Documentar a nova estrutura híbrida.

- [ ] Atualizar README.md com nova estrutura
- [ ] Criar diagramas da arquitetura híbrida
- [ ] Documentar responsabilidades de cada camada
- [ ] Criar guias para novos desenvolvedores

## Estrutura Final - Arquitetura Híbrida

```
src/
├── adapters/                      # � ADAPTERS - Comunicação Externa
│   ├── repositories/              # 🗄️ Repository Adapters
│   │   ├── user/
│   │   │   ├── interface.py       # UserRepositoryInterface
│   │   │   └── repository.py      # UserRepository (implementa interface)
│   │   ├── chat_history/
│   │   │   ├── interface.py       # ChatSessionRepositoryInterface, ChatMessageRepositoryInterface
│   │   │   └── repository.py      # ChatSessionRepository, ChatMessageRepository
│   │   └── llm_model/
│   │       ├── interface.py       # LLMModelRepositoryInterface
│   │       └── repository.py      # LLMModelRepository (implementa interface)
│   ├── auth/                      # 🔐 External Auth Services
│   │   ├── interface.py
│   │   └── provider/
│   │       └── google.py
│   └── llm_chat/                  # 🤖 LLM Providers
│       ├── interface.py
│       └── provider/
│           ├── openai.py
│           └── anthropic.py
├── application/                   # 🧠 APPLICATION - Regras de Negócio
│   ├── auth/
│   │   ├── interface.py          # AuthApplicationInterface
│   │   └── implementation.py     # AuthApplication (usa repository adapters)
│   ├── chat/
│   │   ├── interface.py          # ChatApplicationInterface
│   │   └── implementation.py     # ChatApplication (usa repository adapters)
│   ├── chat_history/
│   │   ├── interface.py          # ChatHistoryApplicationInterface
│   │   ├── implementation.py     # ChatHistoryApplication (usa repository adapters)
│   │   ├── schemas/              # Domain schemas
│   │   └── exceptions/           # Domain exceptions
│   ├── user/
│   │   ├── interface.py          # UserApplicationInterface
│   │   └── implementation.py     # UserApplication (usa repository adapters)
│   └── llm_model/
│       ├── interface.py          # LLMModelApplicationInterface
│       └── implementation.py     # LLMModelApplication (usa repository adapters)
├── infrastructure/               # 🏗️ INFRASTRUCTURE - Database & Config
│   └── database/
│       ├── models/              # SQLAlchemy Models
│       ├── migrations/          # Alembic Migrations
│       ├── seeders/            # Database Seeders
│       └── session.py          # Session Management
├── presentation/                # � PRESENTATION - DTOs e Rotas
│   ├── api/                    # 🚀 REST API & WebSocket
│   │   ├── routers/           # Endpoints (sem regras de negócio)
│   │   ├── schemas/           # DTOs para validação
│   │   └── dependencies/      # Dependency Injection
│   └── web/                   # 🖥️ Streamlit Web UI
└── shared_kernel/             # 🔧 SHARED KERNEL - Código Compartilhado
    ├── config.py             # Configurações globais
    ├── schemas/              # Modelos compartilhados
    └── utils/                # Utilities
```

### Benefícios da Arquitetura Híbrida

#### ✅ **Separação Clara de Responsabilidades**
- **Adapters**: Apenas comunicação externa (banco + APIs)
- **Application**: Apenas regras de negócio
- **Presentation**: Apenas DTOs e rotas
- **Infrastructure**: Apenas configuração de infraestrutura

#### ✅ **Testabilidade Melhorada**
- Application Services testáveis com mocks simples
- Interfaces permitem substituição fácil para testes
- Lógica de negócio isolada e testável

#### ✅ **Manutenibilidade**
- Estrutura organizada e intuitiva
- Responsabilidades bem definidas
- Fácil localização de código por funcionalidade

#### ✅ **Flexibilidade**
- Fácil substituição de tecnologias (banco, LLM providers)
- Novos adapters sem impacto no core
- Múltiplas interfaces (REST, WebSocket, Web UI)

## Sugestões Específicas de Melhoria

### 🔍 **Violações Encontradas que Devem ser Priorizadas**

#### **1. Application Services com SQLAlchemy**
```python
# ❌ CRÍTICO - src/application/user/implementation.py:8-20
def __init__(self, db: Session):  # Application não deveria conhecer Session
    self.db = db
user = self.db.query(User).filter(...)  # Query SQL direta na Application
```
**Sugestão**: Criar `src/adapters/repositories/user/interface.py` e `src/adapters/repositories/user/repository.py`.

#### **2. Presentation importando Adapters**
```python
# ❌ CRÍTICO - src/presentation/web/pages/chat.py:3-6
from src.adapters.llm_chat.interface import LlmChatAdapterInterface
from src.adapters.llm_chat.provider.anthropic import LlmChatAdapterAnthropic
```
**Sugestão**: Presentation deve usar apenas Application Services, não adapters diretamente.

#### **3. Dependencies com Database Direto**
```python
# ❌ CRÍTICO - src/presentation/api/dependencies/auth.py:8-9
from database.database import get_db_session
from database.models.user import User
```
**Sugestão**: Dependencies devem usar apenas Application Services via factory pattern.

### 📋 **Recomendações de Implementação**

#### **Prioridade Alta**
1. **Mover database/ para src/infrastructure/database/**
2. **Criar Repository Modules em src/adapters/repositories/** (ex: `user/`, `chat_history/`, `llm_model/`)
3. **Implementar Repository Interfaces e Adapters** (ex: `user/interface.py` e `user/repository.py`)
4. **Refatorar Application Services (implementation.py) para usar repository adapters via DI**

#### **Prioridade Média**
1. **Limpar Presentation de regras de negócio**
2. **Configurar Dependency Injection adequada**
3. **Atualizar todos os imports**

#### **Prioridade Baixa**
1. **Testes de arquitetura**
2. **Documentação**
3. **Validação final**

## Cronograma de Execução

### **Semana 1-2: Fase 1 - Infrastructure**
- Reorganização da camada Infrastructure
- Criação de Repository Interfaces
- Refatoração dos Application Services

### **Semana 3: Fase 2 - Presentation**
- Limpeza da camada Presentation
- Configuração de Dependency Injection
- Refatoração do WebSocket

### **Semana 4: Fase 3 - Validação**
- Testes de arquitetura
- Documentação
- Validação final

## Critérios de Sucesso

### ✅ **Arquitetura Híbrida Implementada**
- [ ] Application Services dependem apenas de interfaces (não SQLAlchemy)
- [ ] Infrastructure isolada em src/infrastructure/
- [ ] Adapters implementam interfaces definidas na Application
- [ ] Presentation contém apenas DTOs e rotas (sem regras de negócio)

### ✅ **Separação de Responsabilidades**
- [ ] Adapters: apenas comunicação externa
- [ ] Application: apenas regras de negócio
- [ ] Presentation: apenas DTOs e rotas
- [ ] Infrastructure: apenas configuração de infraestrutura
- [ ] Shared Kernel: apenas código compartilhado

### ✅ **Testabilidade Melhorada**
- [ ] Application Services testáveis com mocks simples
- [ ] Interfaces permitem substituição fácil para testes
- [ ] Lógica de negócio isolada e testável
- [ ] Zero dependências diretas de infraestrutura na Application

### ✅ **Flexibilidade Arquitetural**
- [ ] Possível trocar banco de dados sem impacto na Application
- [ ] Novos providers de LLM facilmente adicionáveis via adapters
- [ ] Múltiplas interfaces (REST, WebSocket, Web) sem duplicação de lógica
- [ ] Dependency injection configurável por ambiente

## Próximos Passos Após Implementação

### **Melhorias Futuras (Pós-Refatoração)**

#### **Domain Events**
- Implementar eventos de domínio para desacoplamento
- Event sourcing para auditoria de mudanças
- Integração com message brokers (Redis/RabbitMQ)

#### **CQRS (Command Query Responsibility Segregation)**
- Separar commands (write) de queries (read)
- Otimizar performance de leitura
- Implementar read models específicos

#### **Microservices**
- Dividir aplicação em bounded contexts
- Chat Service, User Service, LLM Service independentes
- API Gateway para roteamento

## Referências

### **Arquitetura Hexagonal**
- [Hexagonal Architecture by Alistair Cockburn](https://alistair.cockburn.us/hexagonal-architecture/)
- [Ports and Adapters Pattern](https://herbertograca.com/2017/09/14/ports-adapters-architecture/)

### **DDD Concepts**
- [Domain-Driven Design by Eric Evans](https://domainlanguage.com/ddd/)
- [Clean Architecture by Robert Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

### **Dependency Injection**
- [Dependency Injection Principles](https://martinfowler.com/articles/injection.html)
- [FastAPI Dependency Injection](https://fastapi.tiangolo.com/tutorial/dependencies/)

### **Testing Strategies**
- [Test Pyramid](https://martinfowler.com/articles/practical-test-pyramid.html)
- [Mocking in Clean Architecture](https://blog.cleancoder.com/uncle-bob/2014/05/14/TheLittleMocker.html)

---

## Status de Implementação

### ✅ **Análise Concluída**
- [x] Análise arquitetural focada em Arquitetura Híbrida (Hexagonal + DDD)
- [x] Identificação de violações específicas da arquitetura proposta
- [x] Plano detalhado seguindo direção: Adapters + Application + Presentation + Infrastructure + Shared Kernel
- [x] Estrutura alvo definida respeitando responsabilidades por camada

### 🎯 **Próximos Passos Imediatos**
1. **Implementar Fase 1** - Reorganização da Infrastructure (1-2 semanas)
2. **Implementar Fase 2** - Limpeza da Presentation (1 semana)
3. **Implementar Fase 3** - Validação e Testes (1 semana)

### 📊 **Progresso Esperado**
- **Semana 1-2**: Criação de Infrastructure e Repository Interfaces
- **Semana 3**: Limpeza da Presentation e Dependency Injection
- **Semana 4**: Validação e documentação

### 🎉 **Resultado Final**
Uma aplicação que segue a **Arquitetura Híbrida** proposta, com:
- **Adapters**: Comunicação com banco e serviços externos
- **Application**: Regras de negócio isoladas (implementação + interfaces)
- **Presentation**: DTOs e rotas sem regras de negócio
- **Infrastructure**: Database dentro da estrutura src/
- **Shared Kernel**: Código compartilhado

---

**Última atualização**: 2025-07-28
**Responsável**: Claude Code Assistant
**Foco**: Arquitetura Híbrida (Hexagonal + DDD Seletivo)
**Status**: ✅ Plano Refatorado - Alinhado com Direção Especificada
