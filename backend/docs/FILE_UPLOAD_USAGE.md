# 📁 File Upload System - Usage Guide

## 🎯 **Overview**

Sistema de upload de arquivos que converte para base64 e envia ao GPT-4o via WebSocket.

### **Fluxo Completo**
1. **Upload**: POST `/api/v1/files/upload` → recebe arquivo → retorna `file_id`
2. **Chat**: WebSocket com `file_ids` → processa arquivos → envia ao GPT-4o
3. **Resposta**: GPT-4o analisa arquivo → streaming response

---

## 📤 **1. Upload de Arquivos**

### **Endpoint**
```
POST /api/v1/files/upload
Content-Type: multipart/form-data
```

### **Request**
```bash
curl -X POST "http://localhost:8501/api/v1/files/upload" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -F "file=@documento.pdf"
```

### **Response**
```json
{
  "file_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "filename": "documento.pdf",
  "file_type": "application/pdf",
  "file_size": 156789,
  "upload_timestamp": "2025-01-29T15:30:00Z",
  "expiry_timestamp": "2025-01-29T16:30:00Z"
}
```

### **Tipos Suportados**
- **PDF**: `application/pdf`
- **Imagens**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- **Texto**: `text/plain`, `text/markdown`, `application/json`, `text/csv`

### **Limites**
- **Tamanho máximo**: 20MB
- **Expiração**: 1 hora após upload
- **Formatos**: Apenas tipos suportados pelo GPT-4o

---

## 💬 **2. Chat com Arquivos**

### **WebSocket Connection**
```javascript
const ws = new WebSocket('ws://localhost:8501/api/v1/chat/ws?token=JWT_TOKEN');
```

### **Mensagem com Arquivo**
```json
{
  "message": "Analise este documento e me dê um resumo",
  "model_id": 1,
  "file_ids": ["a1b2c3d4-e5f6-7890-abcd-ef1234567890"]
}
```

### **Múltiplos Arquivos**
```json
{
  "message": "Compare estes documentos",
  "model_id": 1,
  "file_ids": [
    "file-id-1",
    "file-id-2",
    "file-id-3"
  ]
}
```

### **Responses do WebSocket**

#### **Processamento de Arquivos**
```json
{
  "type": "file_processed",
  "file_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "status": "success"
}
```

#### **Resumo do Processamento**
```json
{
  "type": "files_summary",
  "total_files": 2,
  "successful": 2,
  "failed": 0
}
```

#### **Erro no Arquivo**
```json
{
  "type": "file_error",
  "file_id": "invalid-file-id",
  "error": "File not found: invalid-file-id"
}
```

#### **Streaming Response**
```json
{
  "type": "stream_chunk",
  "chunk": "Este documento parece ser um relatório..."
}
```

### **Histórico de Chat com Arquivos**
Mensagens com arquivos anexados são salvas no histórico:

#### **Mensagem com um arquivo:**
```json
{
  "id": 123,
  "author": "user",
  "content": "Analise este documento",
  "timestamp": "2025-01-29T15:30:00Z",
  "model_id": 1,
  "attached_files": [
    {
      "name": "documento.pdf",
      "mime_type": "application/pdf"
    }
  ]
}
```

#### **Mensagem com múltiplos arquivos:**
```json
{
  "id": 124,
  "author": "user",
  "content": "Compare estes documentos",
  "timestamp": "2025-01-29T15:35:00Z",
  "model_id": 1,
  "attached_files": [
    {
      "name": "relatorio_2023.pdf",
      "mime_type": "application/pdf"
    },
    {
      "name": "grafico.png",
      "mime_type": "image/png"
    },
    {
      "name": "dados.csv",
      "mime_type": "text/csv"
    }
  ]
}
```

---

## 🔧 **3. Gerenciamento de Arquivos**

### **Informações do Arquivo**
```bash
GET /api/v1/files/info/{file_id}
Authorization: Bearer SEU_JWT_TOKEN
```

**Response:**
```json
{
  "file_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "filename": "documento.pdf",
  "file_type": "application/pdf",
  "file_size": 156789,
  "upload_timestamp": "2025-01-29T15:30:00Z",
  "expiry_timestamp": "2025-01-29T16:30:00Z",
  "is_expired": false,
  "is_valid_for_llm": true
}
```

### **Listar Arquivos**
```bash
GET /api/v1/files/list
Authorization: Bearer SEU_JWT_TOKEN
```

### **Deletar Arquivo**
```bash
DELETE /api/v1/files/{file_id}
Authorization: Bearer SEU_JWT_TOKEN
```

### **Limpeza de Arquivos Expirados**
```bash
POST /api/v1/files/cleanup
Authorization: Bearer SEU_JWT_TOKEN
```

### **Estatísticas**
```bash
GET /api/v1/files/stats
Authorization: Bearer SEU_JWT_TOKEN
```

### **Tipos Suportados**
```bash
GET /api/v1/files/supported-types
Authorization: Bearer SEU_JWT_TOKEN
```

---

## 🧪 **4. Exemplos Práticos**

### **Exemplo 1: Upload e Chat com PDF**

```bash
# 1. Upload do PDF
RESPONSE=$(curl -s -X POST "http://localhost:8501/api/v1/files/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@relatorio.pdf")

FILE_ID=$(echo $RESPONSE | jq -r '.file_id')
echo "File ID: $FILE_ID"

# 2. Chat via WebSocket (JavaScript)
const message = {
  "message": "Faça um resumo executivo deste relatório",
  "model_id": 1,
  "file_ids": [FILE_ID]
};

ws.send(JSON.stringify(message));
```

### **Exemplo 2: Upload e Chat com Imagem**

```bash
# 1. Upload da imagem
curl -X POST "http://localhost:8501/api/v1/files/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@grafico.png"

# 2. Chat
{
  "message": "Descreva os dados mostrados neste gráfico",
  "model_id": 1,
  "file_ids": ["image-file-id"]
}
```

### **Exemplo 3: Múltiplos Arquivos**

```javascript
// Upload múltiplos arquivos
const files = ['doc1.pdf', 'doc2.pdf', 'image.png'];
const fileIds = [];

for (const file of files) {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/v1/files/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${jwtToken}`
    },
    body: formData
  });
  
  const result = await response.json();
  fileIds.push(result.file_id);
}

// Chat com todos os arquivos
const message = {
  "message": "Compare estes documentos e identifique as principais diferenças",
  "model_id": 1,
  "file_ids": fileIds
};

ws.send(JSON.stringify(message));
```

---

## ⚠️ **5. Tratamento de Erros**

### **Erros de Upload**
```json
{
  "detail": "Unsupported file type: application/zip"
}
```

```json
{
  "detail": "File too large: 25165824 bytes (max: 20971520)"
}
```

### **Erros de Chat**
```json
{
  "type": "file_error",
  "file_id": "expired-file-id",
  "error": "File has expired: expired-file-id"
}
```

### **Códigos de Status HTTP**
- **200**: Sucesso
- **400**: Arquivo inválido ou tipo não suportado
- **404**: Arquivo não encontrado
- **413**: Arquivo muito grande
- **500**: Erro interno do servidor

---

## 🔒 **6. Segurança**

### **Autenticação**
- **HTTP**: **Requer autenticação** - JWT token obrigatório para todas as rotas
- **WebSocket**: Requer JWT token válido

### **Validações**
- **Tipo MIME**: Verificação rigorosa
- **Tamanho**: Limite de 20MB
- **Nome**: Sanitização automática
- **Expiração**: Limpeza automática após 1 hora

### **Limitações**
- **Rate limiting**: Implementar se necessário
- **Quota por usuário**: Não implementado
- **Scan de vírus**: Não implementado

---

## 📊 **7. Monitoramento**

### **Logs**
- Upload de arquivos
- Processamento para LLM
- Erros e exceções
- Limpeza automática

### **Métricas**
```bash
# Estatísticas atuais
curl http://localhost:8501/api/v1/files/stats
```

### **Health Check**
```bash
# Verificar se sistema está funcionando
curl http://localhost:8501/health
```

---

## 🗄️ **8. Estrutura do Banco de Dados**

### **Tabela chat_messages**
```sql
CREATE TABLE chat_messages (
    id INTEGER PRIMARY KEY,
    session_id INTEGER REFERENCES chat_sessions(id),
    author ENUM('BOT', 'USER'),
    content TEXT,
    model_id INTEGER REFERENCES llm_models(id),
    timestamp DATETIME,
    attached_files JSON  -- Array de arquivos: [{"name": "file.pdf", "mime_type": "application/pdf"}]
);
```

### **Exemplo de dados salvos:**
```sql
INSERT INTO chat_messages (session_id, author, content, model_id, attached_files) VALUES
(1, 'USER', 'Analise estes documentos', 1, '[
  {"name": "relatorio.pdf", "mime_type": "application/pdf"},
  {"name": "grafico.png", "mime_type": "image/png"}
]');
```

### **Query para buscar mensagens com arquivos:**
```sql
-- Buscar mensagens que têm arquivos anexados
SELECT * FROM chat_messages
WHERE attached_files IS NOT NULL
AND JSON_LENGTH(attached_files) > 0;

-- Buscar mensagens com PDFs
SELECT * FROM chat_messages
WHERE JSON_SEARCH(attached_files, 'one', 'application/pdf', NULL, '$[*].mime_type') IS NOT NULL;
```

**Sistema de upload implementado com sucesso! 🚀**
