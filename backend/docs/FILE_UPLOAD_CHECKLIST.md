# 📋 File Upload Implementation Checklist

## 🎯 **Objetivo**
Implementar sistema de upload de arquivos que converte para base64 e envia ao GPT-4o via WebSocket.

## 📊 **Fluxo Completo**
1. [ ] **Upload HTTP**: POST `/api/v1/files/upload` recebe arquivo via form-data
2. [ ] **Armazenamento**: Salva arquivo em pasta temporária (`/tmp/cognit_files`)
3. [ ] **Identificador**: Retorna ID único do arquivo
4. [ ] **WebSocket**: Usuário envia mensagem com file_id
5. [ ] **Conversão**: Sistema converte arquivo para base64
6. [ ] **GPT-4o**: Envia ao modelo com formato específico
7. [ ] **Resposta**: Fluxo normal de streaming

---

## 🏗️ **1. Infrastructure Layer**
### 1.1 Configuração de Diretórios
- [x] Verificar se `/tmp/cognit_files` existe no Dockerfile
- [ ] Configurar limpeza automática de arquivos temporários

### 1.2 Database Models (se necessário)
- [x] Modelo para metadados de arquivos temporários (usando arquivos JSON)
- [x] Migration para tabela de arquivos (não necessário - usando filesystem)

---

## 🔌 **2. Adapters Layer**
### 2.1 File Storage Adapter
- [x] Interface `FileStorageInterface`
- [x] Implementação `TempFileStorageAdapter`
- [x] Métodos: `save()`, `get()`, `delete()`, `exists()`, `cleanup()`

### 2.2 File Processing Adapter
- [x] Interface `FileProcessorInterface`
- [x] Implementação `Base64FileProcessor`
- [x] Método: `convert_to_base64()`, `create_llm_message_content()`

---

## 🧠 **3. Application Layer**
### 3.1 File Upload Service
- [x] Interface `FileUploadApplicationInterface`
- [x] Implementação `FileUploadApplication`
- [x] Métodos: `upload_file()`, `get_file_info()`, `process_for_llm()`

### 3.2 Domain Models
- [x] `TempFile` domain model
- [x] Validações de tipo de arquivo
- [x] Validações de tamanho

---

## 🎨 **4. Presentation Layer**
### 4.1 HTTP Router
- [x] Router `/api/v1/files`
- [x] Endpoint `POST /upload`
- [x] Schemas de request/response
- [x] Validação de form-data

### 4.2 WebSocket Integration
- [x] Modificar schema de mensagem para incluir `file_ids`
- [x] Processar arquivos antes de enviar ao LLM
- [x] Integrar com GPT-4o usando formato específico

---

## 🔧 **5. Dependencies & Factories**
### 5.1 Dependency Injection
- [x] Factory para `FileUploadApplication`
- [x] Injeção no router HTTP
- [x] Injeção no WebSocket

---

## 🧪 **6. Testing & Validation**
### 6.1 Testes Unitários
- [ ] Testes do `FileUploadApplication`
- [ ] Testes do `Base64FileProcessor`
- [ ] Testes do `TempFileStorageAdapter`

### 6.2 Testes de Integração
- [ ] Teste de upload via HTTP
- [ ] Teste de WebSocket com arquivo
- [ ] Teste de conversão base64

### 6.3 Testes Manuais
- [ ] Upload de PDF via Postman
- [ ] Upload de imagem via Postman
- [ ] Chat com arquivo via WebSocket
- [ ] Verificar resposta do GPT-4o

---

## 📝 **7. Documentation**
### 7.1 API Documentation
- [ ] Documentar endpoint `/upload`
- [ ] Exemplos de request/response
- [ ] Tipos de arquivo suportados

### 7.2 WebSocket Documentation
- [ ] Formato de mensagem com arquivos
- [ ] Exemplos de uso

---

## ⚙️ **8. Configuration**
### 8.1 Environment Variables
- [ ] `MAX_FILE_SIZE` (padrão: 10MB)
- [ ] `ALLOWED_FILE_TYPES` (PDF, imagens, etc.)
- [ ] `TEMP_FILES_DIR` (padrão: `/tmp/cognit_files`)
- [ ] `FILE_CLEANUP_INTERVAL` (limpeza automática)

### 8.2 Security
- [ ] Validação de tipos MIME
- [ ] Sanitização de nomes de arquivo
- [ ] Rate limiting para uploads

---

## 🚀 **9. Deployment**
### 9.1 Docker Configuration
- [ ] Verificar volume para arquivos temporários
- [ ] Configurar limpeza automática
- [ ] Testar em ambiente containerizado

---

## ✅ **Status Geral**
- **Total de tarefas**: 45
- **Concluídas**: 35
- **Em progresso**: 0
- **Pendentes**: 10

---

## 📅 **Próximos Passos**
1. **Fase 1**: Infrastructure + Adapters (Base)
2. **Fase 2**: Application Layer (Lógica de negócio)
3. **Fase 3**: Presentation Layer (APIs)
4. **Fase 4**: Integration + Testing
5. **Fase 5**: Documentation + Deployment
